# TradingAgents 环境变量配置文件
# 复制此文件为 .env 并填入您的API密钥

# ===========================================
# 必需的API密钥
# ===========================================

# DeepSeek官方API密钥
# 获取地址: https://platform.deepseek.com/api_keys
OPENAI_API_KEY=***********************************

# FinnHub API密钥 (金融数据，已提供测试密钥)
FINNHUB_API_KEY=d1bu1hhr01qsbpuf9dugd1bu1hhr01qsbpuf9dv0

# ===========================================
# 可选的API密钥
# ===========================================

# 如果您有DeepSeek官方API密钥，可以直接使用
# 获取地址: https://platform.deepseek.com/api_keys
# DEEPSEEK_API_KEY=your_deepseek_api_key_here

# ===========================================
# 网络代理设置 (可选)
# ===========================================

# 如果需要代理访问API，请取消注释并设置
# HTTP_PROXY=http://127.0.0.1:10809
# HTTPS_PROXY=http://127.0.0.1:10809

# ===========================================
# 使用说明
# ===========================================

# 1. 复制此文件为 .env
# 2. 填入您的OpenRouter API密钥
# 3. 运行: python run_deepseek.py

# OpenRouter支持的DeepSeek模型:
# - deepseek/deepseek-chat (标准版)
# - deepseek/deepseek-chat-v3-0324 (V3版本)
# - deepseek/deepseek-coder (代码专用)

# 成本对比 (大约):
# - OpenAI GPT-4: $30/1M tokens
# - DeepSeek Chat: $0.14/1M tokens (便宜200倍+)
