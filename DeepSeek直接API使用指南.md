# DeepSeek 直接 API 使用指南

## ✅ 验证结果

经过测试，DeepSeek 直接 API 连接成功！

```
✅ DeepSeek API 连接成功!
📝 响应: 你好！我是 **DeepSeek Chat**，由深度求索（DeepSeek）公司开发的智能AI助手...
```

## 🚀 快速开始

### 1. 测试 API 连接
```bash
python test_deepseek_api.py
```

### 2. 运行交互式分析
```bash
python run_deepseek_direct.py
```

### 3. 运行自动化分析
```bash
# 默认分析 AAPL
python run_auto_deepseek.py

# 分析特斯拉
python run_auto_deepseek.py TSLA

# 分析英伟达，3天前数据
python run_auto_deepseek.py NVDA 3
```

## 📋 配置信息

### DeepSeek 直接 API 配置
- **API 端点**: `https://api.deepseek.com/v1`
- **模型**: `deepseek-chat`
- **兼容性**: OpenAI API 格式
- **API 密钥**: 已配置 `sk-3dc31...`

### 与 OpenRouter 的区别
| 特性 | DeepSeek 直接 API | OpenRouter API |
|------|------------------|----------------|
| API 端点 | `https://api.deepseek.com/v1` | `https://openrouter.ai/api/v1` |
| 模型名称 | `deepseek-chat` | `deepseek/deepseek-chat` |
| 费用 | DeepSeek 官方定价 | OpenRouter 加价 |
| 速度 | 直连，更快 | 通过代理，稍慢 |
| 稳定性 | 官方服务 | 第三方代理 |

## 🔧 可用脚本

### 1. `test_deepseek_api.py`
- **用途**: 测试 DeepSeek API 连接
- **功能**: 验证 API 密钥、网络连接、模型响应

### 2. `run_deepseek_direct.py`
- **用途**: 交互式运行分析
- **功能**: 手动输入股票代码和日期
- **特点**: 完整的用户交互界面

### 3. `run_auto_deepseek.py`
- **用途**: 自动化批量分析
- **功能**: 命令行参数控制
- **特点**: 适合脚本化和批处理

### 4. `fix_chromadb_issue.py`
- **用途**: 修复 ChromaDB 问题
- **功能**: 替换内存模块，解决 DLL 错误

## 📊 使用示例

### 基础分析
```bash
# 分析苹果股票（默认昨天数据）
python run_auto_deepseek.py AAPL

# 输出文件: deepseek_direct_AAPL_20250128_143022.txt
```

### 高级分析
```bash
# 分析多只股票
python run_auto_deepseek.py TSLA 1
python run_auto_deepseek.py NVDA 2
python run_auto_deepseek.py META 3

# 批量分析脚本
for stock in AAPL TSLA NVDA GOOGL META; do
    python run_auto_deepseek.py $stock 1
    sleep 60  # 避免 API 速率限制
done
```

## 🛠️ 故障排除

### 常见问题

#### 1. ChromaDB DLL 错误
```
ImportError: DLL load failed while importing chromadb_rust_bindings
```
**解决方案**:
```bash
python fix_chromadb_issue.py
```

#### 2. API 连接失败
```
Connection timeout / API key error
```
**解决方案**:
1. 检查网络连接
2. 验证 API 密钥
3. 设置代理（如需要）

#### 3. 速率限制
```
Rate limit exceeded
```
**解决方案**:
1. 等待 1-2 分钟
2. 减少并发请求
3. 升级 API 套餐

#### 4. Yahoo Finance 限制
```
YFRateLimitError: Too Many Requests
```
**解决方案**:
1. 等待 5-10 分钟
2. 使用不同日期
3. 减少数据请求频率

## 🎯 最佳实践

### 1. API 使用优化
- 避免频繁调用
- 合理设置超时时间
- 使用批处理减少请求次数

### 2. 数据获取策略
- 错开时间获取不同股票数据
- 使用缓存避免重复请求
- 选择非高峰时段运行

### 3. 结果管理
- 定期清理旧的分析文件
- 使用有意义的文件命名
- 备份重要的分析结果

## 📈 性能优化

### 网络优化
```python
# 在脚本中设置代理
os.environ['HTTP_PROXY'] = 'http://127.0.0.1:10809'
os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:10809'
```

### 并发控制
```python
import time

# 在批量处理中添加延迟
for ticker in ['AAPL', 'TSLA', 'NVDA']:
    run_analysis(ticker)
    time.sleep(60)  # 等待60秒
```

## 🔐 安全建议

1. **API 密钥管理**
   - 不要在代码中硬编码密钥
   - 使用环境变量存储敏感信息
   - 定期轮换 API 密钥

2. **网络安全**
   - 使用 HTTPS 连接
   - 验证 SSL 证书
   - 避免在不安全网络中使用

## 📞 技术支持

### DeepSeek 官方
- 官网: https://platform.deepseek.com/
- 文档: https://platform.deepseek.com/api-docs/
- 控制台: https://platform.deepseek.com/usage

### 项目相关
- 运行测试: `python test_deepseek_api.py`
- 检查配置: `python -c "from deepseek_config import DEEPSEEK_DIRECT_CONFIG; print(DEEPSEEK_DIRECT_CONFIG)"`
- 修复问题: `python fix_chromadb_issue.py`

## 🎉 总结

现在你已经成功配置了 DeepSeek 直接 API：

✅ **ChromaDB 问题已解决** - 使用内存存储替代  
✅ **DeepSeek API 连接成功** - 直接使用官方 API  
✅ **多种运行方式** - 交互式和自动化脚本  
✅ **完整的故障排除** - 详细的问题解决方案  

享受 DeepSeek 的强大分析能力吧！🚀
