FROM python:3.10-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    git \
    curl \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件 (如果存在)
COPY requirements.txt* ./

# 复制项目文件
COPY . .

# 安装Python依赖
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -e .

# 设置API密钥环境变量
ENV OPENAI_API_KEY=sk-3dc3163acbba4985ad492657b6bba7d5
ENV FINNHUB_API_KEY=d1bu1hhr01qsbpuf9dugd1bu1hhr01qsbpuf9dv0

# 创建非root用户
RUN useradd -m -u 1000 tradingagents && \
    chown -R tradingagents:tradingagents /app
USER tradingagents

# 暴露端口 (如果需要Web界面)
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "from tradingagents.graph.trading_graph import TradingAgentsGraph; print('OK')" || exit 1

# 默认命令
CMD ["python", "run_deepseek.py"]
