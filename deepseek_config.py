import os
from tradingagents.default_config import DEFAULT_CONFIG

# DeepSeek专用配置
DEEPSEEK_CONFIG = DEFAULT_CONFIG.copy()

# 更新为DeepSeek配置
DEEPSEEK_CONFIG.update({
    # LLM设置 - 使用OpenRouter访问DeepSeek
    "llm_provider": "openrouter",
    "deep_think_llm": "deepseek/deepseek-chat",  # DeepSeek主力模型
    "quick_think_llm": "deepseek/deepseek-chat",  # 同样使用DeepSeek保持一致性
    "backend_url": "https://openrouter.ai/api/v1",
    
    # 优化的辩论和讨论设置
    "max_debate_rounds": 2,  # DeepSeek性能好，可以增加辩论轮数
    "max_risk_discuss_rounds": 2,
    "max_recur_limit": 100,
    
    # 工具设置
    "online_tools": True,  # 启用在线工具获取最新数据
})

# 可选的DeepSeek V3配置（更强大的模型）
DEEPSEEK_V3_CONFIG = DEFAULT_CONFIG.copy()
DEEPSEEK_V3_CONFIG.update({
    "llm_provider": "openrouter", 
    "deep_think_llm": "deepseek/deepseek-chat-v3-0324",  # DeepSeek V3
    "quick_think_llm": "deepseek/deepseek-chat",  # 快速思考仍用标准版
    "backend_url": "https://openrouter.ai/api/v1",
    "max_debate_rounds": 3,  # V3模型更强，可以更多轮辩论
    "max_risk_discuss_rounds": 2,
    "online_tools": True,
})

# 直接使用DeepSeek API的配置（如果有DeepSeek API密钥）
DEEPSEEK_DIRECT_CONFIG = DEFAULT_CONFIG.copy()
DEEPSEEK_DIRECT_CONFIG.update({
    "llm_provider": "openai",  # 使用OpenAI兼容接口
    "deep_think_llm": "deepseek-chat",
    "quick_think_llm": "deepseek-chat",
    "backend_url": "https://api.deepseek.com/v1",  # DeepSeek官方API
    "max_debate_rounds": 2,
    "max_risk_discuss_rounds": 2,
    "online_tools": True,
    # 中文输出配置
    "language": "zh-CN",
    "output_language": "中文",
    "system_prompt_suffix": "请用中文回答所有问题。",
})

def setup_deepseek_env():
    """设置DeepSeek环境变量"""
    print("🔧 设置DeepSeek环境...")
    
    # 检查API密钥
    if not os.getenv('OPENAI_API_KEY'):
        print("⚠️  请设置OpenRouter API密钥:")
        print("   export OPENAI_API_KEY='your_openrouter_api_key'")
        print("   或者在代码中设置: os.environ['OPENAI_API_KEY'] = 'your_key'")
    
    # 可选：设置代理
    if not os.getenv('HTTP_PROXY'):
        print("💡 如需代理，请设置:")
        print("   export HTTP_PROXY='http://127.0.0.1:10809'")
        print("   export HTTPS_PROXY='http://127.0.0.1:10809'")
    
    print("✅ DeepSeek环境配置完成!")

def get_deepseek_config(version="standard"):
    """获取DeepSeek配置
    
    Args:
        version: "standard", "v3", 或 "direct"
    """
    configs = {
        "standard": DEEPSEEK_CONFIG,
        "v3": DEEPSEEK_V3_CONFIG, 
        "direct": DEEPSEEK_DIRECT_CONFIG
    }
    
    if version not in configs:
        raise ValueError(f"不支持的版本: {version}. 支持的版本: {list(configs.keys())}")
    
    return configs[version]

if __name__ == "__main__":
    setup_deepseek_env()
    print("\n📋 可用的DeepSeek配置:")
    print("1. standard - 标准DeepSeek配置 (通过OpenRouter)")
    print("2. v3 - DeepSeek V3配置 (更强大)")
    print("3. direct - 直接DeepSeek API配置")
