DeepSeek 中文金融分析系统 - 完整控制台输出
============================================================
运行时间: 2025-07-29 20:14:16
股票代码: AAPL
API 端点: https://api.deepseek.com/v1
使用模型: deepseek-chat
============================================================

=== 控制台输出内容 ===

🔧 DeepSeek 中文金融分析系统
📋 使用说明:
   python run_deepseek_chinese.py [股票代码] [天数]
   示例: python run_deepseek_chinese.py TSLA 2
   默认: python run_deepseek_chinese.py AAPL 1

📊 当前分析参数:
   股票代码: AAPL
   分析日期: 1 天前
   输出语言: 中文
   API 类型: DeepSeek 直接 API

🚀 DeepSeek 中文金融分析系统
============================================================
✅ 已设置 DeepSeek API 密钥: sk-3dc31...
✅ 已设置 FinnHub API 密钥

🔍 网络连接测试...
   测试直连到 DeepSeek API...
   ✅ 直连成功 (状态码: 404)

✅ API 端点: https://api.deepseek.com/v1
✅ 模型: deepseek-chat
✅ 输出语言: 中文

📊 分析设置:
   股票代码: AAPL
   分析日期: 2025-07-28
   使用模型: deepseek-chat
   输出语言: 中文
   分析师团队: 市场分析师、社交媒体分析师、新闻分析师、基础面分析师

🔄 正在初始化 DeepSeek 中文分析系统...
✅ 系统初始化成功!

🤖 DeepSeek 开始中文深度分析 AAPL (日期: 2025-07-28)...
⏳ 正在调用 DeepSeek 官方 API，生成中文分析报告...
   预计需要 3-5 分钟时间...
================================ Human Message =================================

AAPL
