DeepSeek 中文金融分析系统 - 完整控制台输出
============================================================
运行时间: 2025-07-29 01:20:52
股票代码: TSLA
API 端点: https://api.deepseek.com/v1
使用模型: deepseek-chat
============================================================

=== 控制台输出内容 ===

🔧 DeepSeek 中文金融分析系统
📋 使用说明:
   python run_deepseek_chinese.py [股票代码] [天数]
   示例: python run_deepseek_chinese.py TSLA 2
   默认: python run_deepseek_chinese.py AAPL 1

📊 当前分析参数:
   股票代码: TSLA
   分析日期: 1 天前
   输出语言: 中文
   API 类型: DeepSeek 直接 API

🚀 DeepSeek 中文金融分析系统
============================================================
✅ 已设置 DeepSeek API 密钥: sk-3dc31...
✅ 已设置 FinnHub API 密钥
⚠️ 代理不可用，使用直连
✅ API 端点: https://api.deepseek.com/v1
✅ 模型: deepseek-chat
✅ 输出语言: 中文

📊 分析设置:
   股票代码: TSLA
   分析日期: 2025-07-28
   使用模型: deepseek-chat
   输出语言: 中文
   分析师团队: 市场分析师、社交媒体分析师、新闻分析师、基础面分析师

🔄 正在初始化 DeepSeek 中文分析系统...
✅ 系统初始化成功!

🤖 DeepSeek 开始中文深度分析 TSLA (日期: 2025-07-28)...
⏳ 正在调用 DeepSeek 官方 API，生成中文分析报告...
   预计需要 3-5 分钟时间...
================================ Human Message =================================

TSLA
================================== Ai Message ==================================
Tool Calls:
  get_YFin_data_online (call_0_0585c944-30d3-472b-a57a-cf5039a11b04)
 Call ID: call_0_0585c944-30d3-472b-a57a-cf5039a11b04
  Args:
    symbol: TSLA
    start_date: 2025-06-28
    end_date: 2025-07-28
================================= Tool Message =================================
Name: get_YFin_data_online

Error: YFRateLimitError('Too Many Requests. Rate limited. Try after a while.')
 Please fix your mistakes.
================================== Ai Message ==================================

It seems there was an issue retrieving the data due to rate limits. Let me try again to fetch the data for TSLA.
Tool Calls:
  get_YFin_data_online (call_0_8f9e1dc7-c527-428d-bdb6-ded30b12d5bd)
 Call ID: call_0_8f9e1dc7-c527-428d-bdb6-ded30b12d5bd
  Args:
    symbol: TSLA
    start_date: 2025-06-28
    end_date: 2025-07-28
================================= Tool Message =================================
Name: get_YFin_data_online

Error: YFRateLimitError('Too Many Requests. Rate limited. Try after a while.')
 Please fix your mistakes.
================================== Ai Message ==================================

It appears that the Yahoo Finance API is currently rate-limited, and we are unable to fetch the data at this moment. Let me wait a brief moment and try again.
Tool Calls:
  get_YFin_data_online (call_0_d03c90c7-0cb1-4bb5-8358-6a7343053489)
 Call ID: call_0_d03c90c7-0cb1-4bb5-8358-6a7343053489
  Args:
    symbol: TSLA
    start_date: 2025-06-28
    end_date: 2025-07-28
================================= Tool Message =================================
Name: get_YFin_data_online

Error: YFRateLimitError('Too Many Requests. Rate limited. Try after a while.')
 Please fix your mistakes.
================================== Ai Message ==================================

Unfortunately, the Yahoo Finance API is still rate-limited, and we cannot retrieve the data for TSLA at this time. Let me know if you'd like me to proceed with a different approach or try again later. Alternatively, if you have the data available, you can provide it, and I can analyze it for you.
================================ Human Message =================================

Continue
================================== Ai Message ==================================
Tool Calls:
  get_stock_news_openai (call_0_cdeeff88-73ee-4f62-a981-f8bfedea388c)
 Call ID: call_0_cdeeff88-73ee-4f62-a981-f8bfedea388c
  Args:
    ticker: TSLA
    curr_date: 2025-07-28
