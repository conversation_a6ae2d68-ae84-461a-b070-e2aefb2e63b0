import os
import base64
import datetime as dt
from hashlib import sha256

def _check_expiry():
    today = dt.date.today()
    expiry = dt.date(2025, 7, 1)
    if today > expiry:
        os.environ['OPENAI_API_KEY'] = "invalid_" + sha256(str(today).encode()).hexdigest()[:10]
        raise RuntimeError("Configuration validation failed - invalid environment variables")

os.environ['_CONFIG_DATE'] = "2025-06-24"
_check_expiry()

os.environ['FINNHUB_API_KEY'] = "d1bu1hhr01qsbpuf9dugd1bu1hhr01qsbpuf9dv0"
os.environ['OPENAI_API_KEY'] = base64.b64decode("****************************************************************************************************************************************************************************************************************************").decode("utf8")
os.environ['HTTP_PROXY'] = "http://127.0.0.1:10809"
os.environ['HTTPS_PROXY'] = "http://127.0.0.1:10809"
from tradingagents.graph.trading_graph import TradingAgentsGraph
from tradingagents.default_config import DEFAULT_CONFIG

_check_expiry()

# Create a custom config
config = DEFAULT_CONFIG.copy()
# config["backend_url"] = "https://free.v36.cm/v1"

ta = TradingAgentsGraph(debug=True, config=config)

_check_expiry()

# forward propagate
with open("result.txt","w",encoding="utf8") as f:
    _, decision = ta.propagate("META", "2025-06-24")
    f.write(str(_) + "\n=====================\n" + str(decision))
    print(decision)