#!/usr/bin/env python3
"""
修复 ChromaDB DLL 加载问题的脚本
解决 Windows 上 "DLL load failed while importing chromadb_rust_bindings" 错误
"""

import os
import sys
import shutil
from pathlib import Path

def backup_original_file():
    """备份原始文件"""
    memory_file = Path("tradingagents/agents/utils/memory.py")
    backup_file = Path("tradingagents/agents/utils/memory.py.backup")
    
    if memory_file.exists() and not backup_file.exists():
        shutil.copy2(memory_file, backup_file)
        print(f"✅ 已备份原始文件到: {backup_file}")
    elif backup_file.exists():
        print(f"ℹ️ 备份文件已存在: {backup_file}")

def create_fixed_memory_module():
    """创建修复后的内存模块"""
    
    fixed_code = '''import json
import numpy as np
from openai import OpenAI
from typing import List, Dict, Any


class FinancialSituationMemory:
    def __init__(self, name, config):
        if config["backend_url"] == "http://localhost:11434/v1":
            self.embedding = "nomic-embed-text"
            self.use_openai = False
        else:
            self.embedding = "text-embedding-3-small"
            self.client = OpenAI()
            self.use_openai = True
        
        # 使用简单的内存存储替代 ChromaDB
        self.name = name
        self.situations = []  # 存储情况文本
        self.recommendations = []  # 存储建议
        self.embeddings = []  # 存储嵌入向量

    def get_embedding(self, text):
        """Get embedding for a text"""
        if self.use_openai:
            response = self.client.embeddings.create(
                model=self.embedding, input=text
            )
            return response.data[0].embedding
        else:
            # 简单的文本向量化（用于本地模型）
            # 这里使用简单的词频向量作为示例
            words = text.lower().split()
            # 创建一个简单的词汇表
            vocab = set()
            for situation in self.situations:
                vocab.update(situation.lower().split())
            vocab.update(words)
            vocab = sorted(list(vocab))
            
            # 创建词频向量
            vector = [words.count(word) for word in vocab]
            # 归一化
            norm = np.linalg.norm(vector) if np.linalg.norm(vector) > 0 else 1
            return [v / norm for v in vector]

    def add_situations(self, situations_and_advice):
        """Add financial situations and their corresponding advice. Parameter is a list of tuples (situation, rec)"""
        for situation, recommendation in situations_and_advice:
            self.situations.append(situation)
            self.recommendations.append(recommendation)
            self.embeddings.append(self.get_embedding(situation))

    def cosine_similarity(self, vec1, vec2):
        """计算余弦相似度"""
        if len(vec1) != len(vec2):
            # 如果向量长度不同，填充较短的向量
            max_len = max(len(vec1), len(vec2))
            vec1 = vec1 + [0] * (max_len - len(vec1))
            vec2 = vec2 + [0] * (max_len - len(vec2))
        
        dot_product = sum(a * b for a, b in zip(vec1, vec2))
        norm1 = sum(a * a for a in vec1) ** 0.5
        norm2 = sum(b * b for b in vec2) ** 0.5
        
        if norm1 == 0 or norm2 == 0:
            return 0
        return dot_product / (norm1 * norm2)

    def get_memories(self, current_situation, n_matches=1):
        """Find matching recommendations using embeddings"""
        if not self.situations:
            return []
            
        query_embedding = self.get_embedding(current_situation)
        
        # 计算与所有存储情况的相似度
        similarities = []
        for i, stored_embedding in enumerate(self.embeddings):
            similarity = self.cosine_similarity(query_embedding, stored_embedding)
            similarities.append((similarity, i))
        
        # 按相似度排序并取前n个
        similarities.sort(reverse=True)
        top_matches = similarities[:n_matches]
        
        matched_results = []
        for similarity, idx in top_matches:
            matched_results.append({
                "matched_situation": self.situations[idx],
                "recommendation": self.recommendations[idx],
                "similarity_score": similarity,
            })
        
        return matched_results


if __name__ == "__main__":
    # Example usage
    config = {"backend_url": "http://localhost:11434/v1"}  # 或者其他配置
    matcher = FinancialSituationMemory("test_memory", config)

    # Example data
    example_data = [
        (
            "High inflation rate with rising interest rates and declining consumer spending",
            "Consider defensive sectors like consumer staples and utilities. Review fixed-income portfolio duration.",
        ),
        (
            "Tech sector showing high volatility with increasing institutional selling pressure",
            "Reduce exposure to high-growth tech stocks. Look for value opportunities in established tech companies with strong cash flows.",
        ),
        (
            "Strong dollar affecting emerging markets with increasing forex volatility",
            "Hedge currency exposure in international positions. Consider reducing allocation to emerging market debt.",
        ),
        (
            "Market showing signs of sector rotation with rising yields",
            "Rebalance portfolio to maintain target allocations. Consider increasing exposure to sectors benefiting from higher rates.",
        ),
    ]

    # Add the example situations and recommendations
    matcher.add_situations(example_data)

    # Example query
    current_situation = """
    Market showing increased volatility in tech sector, with institutional investors 
    reducing positions and rising interest rates affecting growth stock valuations
    """

    try:
        recommendations = matcher.get_memories(current_situation, n_matches=2)

        for i, rec in enumerate(recommendations, 1):
            print(f"\\nMatch {i}:")
            print(f"Similarity Score: {rec['similarity_score']:.2f}")
            print(f"Matched Situation: {rec['matched_situation']}")
            print(f"Recommendation: {rec['recommendation']}")

    except Exception as e:
        print(f"Error during recommendation: {str(e)}")
'''
    
    memory_file = Path("tradingagents/agents/utils/memory.py")
    
    # 确保目录存在
    memory_file.parent.mkdir(parents=True, exist_ok=True)
    
    # 写入修复后的代码
    with open(memory_file, 'w', encoding='utf-8') as f:
        f.write(fixed_code)
    
    print(f"✅ 已创建修复后的内存模块: {memory_file}")

def test_fixed_module():
    """测试修复后的模块"""
    try:
        # 添加项目路径
        sys.path.insert(0, os.getcwd())
        
        from tradingagents.agents.utils.memory import FinancialSituationMemory
        
        # 测试配置
        config = {"backend_url": "https://api.deepseek.com/v1"}
        memory = FinancialSituationMemory("test", config)
        
        # 测试数据
        test_data = [
            ("Market volatility increasing", "Consider defensive positions"),
            ("Tech stocks declining", "Look for value opportunities")
        ]
        
        memory.add_situations(test_data)
        results = memory.get_memories("High market volatility", n_matches=1)
        
        if results:
            print("✅ 内存模块测试成功!")
            print(f"   测试结果: {results[0]['matched_situation']}")
            return True
        else:
            print("⚠️ 内存模块测试返回空结果")
            return False
            
    except Exception as e:
        print(f"❌ 内存模块测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 修复 ChromaDB DLL 加载问题")
    print("=" * 50)
    
    # 检查是否在正确的目录
    if not Path("tradingagents").exists():
        print("❌ 错误: 请在项目根目录运行此脚本")
        print("   当前目录应包含 'tradingagents' 文件夹")
        sys.exit(1)
    
    # 备份原始文件
    backup_original_file()
    
    # 创建修复后的模块
    create_fixed_memory_module()
    
    # 测试修复后的模块
    print("\n🧪 测试修复后的模块...")
    if test_fixed_module():
        print("\n🎉 修复完成!")
        print("现在可以正常运行项目了:")
        print("   python run_deepseek.py")
        print("   或")
        print("   python run_auto.py")
    else:
        print("\n⚠️ 修复可能不完整，请检查错误信息")
    
    print("\n📋 修复说明:")
    print("1. 移除了对 ChromaDB 的依赖")
    print("2. 使用简单的内存存储替代")
    print("3. 保持了相同的 API 接口")
    print("4. 原始文件已备份为 .backup")

if __name__ == "__main__":
    main()
