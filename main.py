import os
import base64

os.environ['FINNHUB_API_KEY'] = "d1bu1hhr01qsbpuf9dugd1bu1hhr01qsbpuf9dv0"

# 🔑 在这里设置您的DeepSeek API密钥
YOUR_DEEPSEEK_API_KEY = "***********************************"

if YOUR_DEEPSEEK_API_KEY:
    os.environ['OPENAI_API_KEY'] = YOUR_DEEPSEEK_API_KEY
else:
    # 如果没有设置，使用默认值（需要您手动替换）
    os.environ['OPENAI_API_KEY'] = "your_deepseek_api_key_here"  # 请替换为您的DeepSeek API密钥
# 如果需要代理，请取消注释下面的行
# os.environ['HTTP_PROXY'] = "http://127.0.0.1:10809"
# os.environ['HTTPS_PROXY'] = "http://127.0.0.1:10809"
from tradingagents.graph.trading_graph import TradingAgentsGraph
from tradingagents.default_config import DEFAULT_CONFIG

# Create a custom config for DeepSeek
config = DEFAULT_CONFIG.copy()
config["llm_provider"] = "openai"  # 使用OpenAI兼容接口访问DeepSeek
config["backend_url"] = "https://api.deepseek.com"  # DeepSeek官方API端点
config["deep_think_llm"] = "deepseek-chat"  # DeepSeek深度思考模型
config["quick_think_llm"] = "deepseek-chat"  # DeepSeek快速思考模型
config["max_debate_rounds"] = 1  # 辩论轮数
config["online_tools"] = True  # 使用在线工具

# Initialize with custom config
ta = TradingAgentsGraph(debug=True, config=config)

# forward propagate
_, decision = ta.propagate("NVDA", "2024-05-10")
print(decision)

# Memorize mistakes and reflect
# ta.reflect_and_remember(1000) # parameter is the position returns
