#!/usr/bin/env python3
"""
DeepSeek 自动化运行脚本
直接使用 DeepSeek 官方 API，无需交互，自动运行分析
"""

import os
import sys
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tradingagents.graph.trading_graph import TradingAgentsGraph
from deepseek_config import DEEPSEEK_DIRECT_CONFIG

def setup_environment():
    """自动设置环境"""
    print("🚀 DeepSeek 直接 API 金融交易分析系统 (自动模式)")
    print("=" * 60)
    
    # 设置 DeepSeek API 密钥
    api_key = "***********************************"
    os.environ['OPENAI_API_KEY'] = api_key
    print(f"✅ 已设置 DeepSeek API 密钥: {api_key[:8]}...")
    
    # 设置 FinnHub API 密钥
    os.environ['FINNHUB_API_KEY'] = "d1bu1hhr01qsbpuf9dugd1bu1hhr01qsbpuf9dv0"
    print("✅ 已设置 FinnHub API 密钥")
    
    # 自动检测并设置代理
    proxy_url = "http://127.0.0.1:10809"
    try:
        import requests
        # 测试代理是否可用
        response = requests.get("https://httpbin.org/ip", 
                              proxies={"http": proxy_url, "https": proxy_url}, 
                              timeout=5)
        if response.status_code == 200:
            os.environ['HTTP_PROXY'] = proxy_url
            os.environ['HTTPS_PROXY'] = proxy_url
            print(f"✅ 代理已设置: {proxy_url}")
        else:
            print("⚠️ 代理测试失败，跳过代理设置")
    except:
        print("⚠️ 代理不可用，跳过代理设置")
    
    print(f"✅ API 端点: {DEEPSEEK_DIRECT_CONFIG['backend_url']}")
    print(f"✅ 模型: {DEEPSEEK_DIRECT_CONFIG['deep_think_llm']}")

def run_analysis(ticker="AAPL", days_ago=1):
    """运行 DeepSeek 分析
    
    Args:
        ticker: 股票代码，默认 AAPL
        days_ago: 分析几天前的数据，默认 1 天前
    """
    
    # 设置环境
    setup_environment()
    
    # 计算分析日期
    target_date = datetime.now() - timedelta(days=days_ago)
    date_str = target_date.strftime("%Y-%m-%d")
    
    print(f"\n📊 分析设置:")
    print(f"   股票代码: {ticker}")
    print(f"   分析日期: {date_str}")
    print(f"   使用模型: {DEEPSEEK_DIRECT_CONFIG['deep_think_llm']}")
    print(f"   API 端点: {DEEPSEEK_DIRECT_CONFIG['backend_url']}")
    print(f"   分析师团队: 市场、社交媒体、新闻、基础面")
    
    try:
        print(f"\n🔄 正在初始化 DeepSeek 交易分析系统...")
        ta = TradingAgentsGraph(
            debug=True, 
            config=DEEPSEEK_DIRECT_CONFIG,
            selected_analysts=["market", "social", "news", "fundamentals"]
        )
        print("✅ 系统初始化成功!")
        
        print(f"\n🤖 DeepSeek 开始深度分析 {ticker} (日期: {date_str})...")
        print("⏳ 正在调用 DeepSeek 官方 API，请耐心等待...")
        print("   预计需要 3-5 分钟时间...")
        
        # 执行分析
        state, decision = ta.propagate(ticker, date_str)
        
        print("\n" + "="*70)
        print("🎯 DeepSeek 直接 API 分析结果")
        print("="*70)
        print(decision)
        print("="*70)
        
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"deepseek_direct_{ticker}_{timestamp}.txt"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"DeepSeek 直接 API 金融分析报告\n")
            f.write(f"{'='*50}\n")
            f.write(f"API 端点: {DEEPSEEK_DIRECT_CONFIG['backend_url']}\n")
            f.write(f"使用模型: {DEEPSEEK_DIRECT_CONFIG['deep_think_llm']}\n")
            f.write(f"股票代码: {ticker}\n")
            f.write(f"分析日期: {date_str}\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"{'='*50}\n\n")
            f.write(decision)
        
        print(f"\n💾 分析结果已保存到: {filename}")
        return True
        
    except Exception as e:
        print(f"\n❌ 分析过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        
        error_msg = str(e).lower()
        if "api" in error_msg or "key" in error_msg:
            print("\n💡 API 相关问题解决方案:")
            print("1. 检查 DeepSeek API 密钥是否正确")
            print("2. 确认 DeepSeek 账户余额充足")
            print("3. 访问 https://platform.deepseek.com/ 检查账户状态")
            print("4. 确认 API 密钥有正确的权限")
        elif "rate" in error_msg or "limit" in error_msg:
            print("\n💡 速率限制解决方案:")
            print("1. 等待 5-10 分钟后重试")
            print("2. 检查 API 调用频率")
            print("3. 考虑升级 DeepSeek 账户套餐")
        elif "network" in error_msg or "connection" in error_msg:
            print("\n💡 网络问题解决方案:")
            print("1. 检查网络连接")
            print("2. 确认代理设置正确")
            print("3. 尝试更换网络环境")
        
        return False

def main():
    """主函数"""
    print("🔧 DeepSeek 直接 API 自动化分析")
    print("📋 使用说明:")
    print("   python run_auto_deepseek.py [股票代码] [天数]")
    print("   示例: python run_auto_deepseek.py TSLA 2")
    print("   默认: python run_auto_deepseek.py AAPL 1")
    print()
    
    # 解析命令行参数
    if len(sys.argv) > 1:
        ticker = sys.argv[1].upper()
    else:
        ticker = "AAPL"  # 默认分析苹果股票
    
    if len(sys.argv) > 2:
        try:
            days_ago = int(sys.argv[2])
        except ValueError:
            print("⚠️ 天数参数无效，使用默认值 1")
            days_ago = 1
    else:
        days_ago = 1  # 默认分析昨天的数据
    
    print(f"📊 当前分析参数:")
    print(f"   股票代码: {ticker}")
    print(f"   分析日期: {days_ago} 天前")
    print(f"   API 类型: DeepSeek 直接 API")
    print()
    
    success = run_analysis(ticker, days_ago)
    
    if success:
        print("\n🎉 DeepSeek 直接 API 分析完成!")
        print("✨ 享受 DeepSeek 的深度思考能力!")
    else:
        print("\n❌ 分析失败!")
        print("💡 请检查错误信息并重试")
        sys.exit(1)

if __name__ == "__main__":
    main()
