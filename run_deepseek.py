#!/usr/bin/env python3
"""
DeepSeek专用运行脚本
使用DeepSeek模型进行金融交易分析
"""

import os
import sys
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tradingagents.graph.trading_graph import TradingAgentsGraph
from deepseek_config import get_deepseek_config, setup_deepseek_env

def setup_api_keys():
    """设置必要的API密钥"""
    # FinnHub API密钥 (金融数据)
    os.environ['FINNHUB_API_KEY'] = "d1bu1hhr01qsbpuf9dugd1bu1hhr01qsbpuf9dv0"

    # 🔑 在这里直接设置您的DeepSeek API密钥
    # 方式1: 直接设置 (推荐)
    YOUR_DEEPSEEK_API_KEY = "***********************************"

    if YOUR_DEEPSEEK_API_KEY and YOUR_DEEPSEEK_API_KEY != "在这里粘贴您的DeepSeek API密钥":
        os.environ['OPENAI_API_KEY'] = YOUR_DEEPSEEK_API_KEY
        print(f"✅ 已设置DeepSeek API密钥: {YOUR_DEEPSEEK_API_KEY[:8]}...")
    elif not os.getenv('OPENAI_API_KEY'):
        print("⚠️  请设置您的DeepSeek API密钥:")
        print("   方式1: 在代码中修改 YOUR_DEEPSEEK_API_KEY 变量")
        print("   方式2: 手动输入")
        api_key = input("请输入DeepSeek API密钥 (或按Enter跳过): ").strip()
        if api_key:
            os.environ['OPENAI_API_KEY'] = api_key
            print(f"✅ 已设置API密钥: {api_key[:8]}...")
        else:
            print("⚠️  未设置API密钥，将使用演示模式")
            os.environ['OPENAI_API_KEY'] = "demo_key"
    
    # 可选：设置代理
    proxy = input("是否需要设置代理? (y/N): ").strip().lower()
    if proxy == 'y':
        proxy_url = input("请输入代理地址 (例: http://127.0.0.1:10809): ").strip()
        if proxy_url:
            os.environ['HTTP_PROXY'] = proxy_url
            os.environ['HTTPS_PROXY'] = proxy_url
            print(f"✅ 代理已设置: {proxy_url}")

def select_config():
    """选择DeepSeek配置"""
    print("\n🤖 选择DeepSeek配置:")
    print("1. 标准版 (deepseek-chat)")
    print("2. V3版本 (deepseek-chat-v3) - 更强大")
    print("3. 直接API (需要DeepSeek API密钥)")
    
    choice = input("请选择配置 (1-3, 默认1): ").strip()
    
    config_map = {
        "1": "standard",
        "2": "v3", 
        "3": "direct",
        "": "standard"  # 默认
    }
    
    version = config_map.get(choice, "standard")
    return get_deepseek_config(version)

def get_stock_input():
    """获取股票和日期输入"""
    print("\n📈 股票分析设置:")
    
    # 股票代码
    ticker = input("请输入股票代码 (例: AAPL, TSLA, NVDA): ").strip().upper()
    if not ticker:
        ticker = "AAPL"  # 默认
        print(f"使用默认股票: {ticker}")
    
    # 分析日期
    print("\n📅 分析日期 (必须是过去的日期):")
    date_str = input("请输入日期 (YYYY-MM-DD, 默认昨天): ").strip()
    
    if not date_str:
        # 默认使用昨天
        yesterday = datetime.now() - timedelta(days=1)
        date_str = yesterday.strftime("%Y-%m-%d")
        print(f"使用默认日期: {date_str}")
    
    return ticker, date_str

def run_analysis():
    """运行DeepSeek分析"""
    print("🚀 DeepSeek金融交易分析系统")
    print("=" * 50)
    
    # 设置环境
    setup_deepseek_env()
    setup_api_keys()
    
    # 选择配置
    config = select_config()
    print(f"\n✅ 已选择配置: {config['deep_think_llm']}")
    
    # 获取输入
    ticker, date_str = get_stock_input()
    
    # 选择分析师
    print("\n👥 分析师团队:")
    print("将使用所有分析师: 市场分析师、社交媒体分析师、新闻分析师、基础面分析师")
    
    try:
        print(f"\n🔄 正在初始化DeepSeek交易分析系统...")
        ta = TradingAgentsGraph(
            debug=True, 
            config=config,
            selected_analysts=["market", "social", "news", "fundamentals"]
        )
        print("✅ 系统初始化成功!")
        
        print(f"\n📊 开始分析 {ticker} (日期: {date_str})...")
        print("⏳ 这可能需要几分钟时间，请耐心等待...")
        
        # 执行分析
        state, decision = ta.propagate(ticker, date_str)
        
        print("\n" + "="*60)
        print("🎯 DeepSeek分析结果")
        print("="*60)
        print(decision)
        print("="*60)
        
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"deepseek_analysis_{ticker}_{timestamp}.txt"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"DeepSeek金融分析报告\n")
            f.write(f"股票代码: {ticker}\n")
            f.write(f"分析日期: {date_str}\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"使用模型: {config['deep_think_llm']}\n")
            f.write("\n" + "="*60 + "\n")
            f.write(decision)
        
        print(f"\n💾 分析结果已保存到: {filename}")
        
    except Exception as e:
        print(f"\n❌ 分析过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        
        if "API" in str(e) or "key" in str(e).lower():
            print("\n💡 可能的解决方案:")
            print("1. 检查OpenRouter API密钥是否正确")
            print("2. 确认账户余额充足")
            print("3. 检查网络连接和代理设置")

if __name__ == "__main__":
    run_analysis()
