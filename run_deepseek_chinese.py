#!/usr/bin/env python3
"""
DeepSeek 中文版运行脚本
使用 DeepSeek 官方 API，输出中文分析报告
"""

import os
import sys
import io
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tradingagents.graph.trading_graph import TradingAgentsGraph
from deepseek_config import DEEPSEEK_DIRECT_CONFIG

# 用于捕获控制台输出的类
class ConsoleCapture:
    def __init__(self):
        self.captured_output = []
        self.original_stdout = sys.stdout
        self.original_stderr = sys.stderr

    def write(self, text):
        # 同时输出到控制台和捕获
        self.original_stdout.write(text)
        self.original_stdout.flush()
        self.captured_output.append(text)

    def flush(self):
        self.original_stdout.flush()

    def get_output(self):
        return ''.join(self.captured_output)

def setup_chinese_environment():
    """设置中文环境"""
    print("🚀 DeepSeek 中文金融分析系统")
    print("=" * 60)

    # 设置 DeepSeek API 密钥
    api_key = "***********************************"
    os.environ['OPENAI_API_KEY'] = api_key
    print(f"✅ 已设置 DeepSeek API 密钥: {api_key[:8]}...")

    # 设置 FinnHub API 密钥
    os.environ['FINNHUB_API_KEY'] = "d1bu1hhr01qsbpuf9dugd1bu1hhr01qsbpuf9dv0"
    print("✅ 已设置 FinnHub API 密钥")

    # 网络连接测试和代理设置
    print("\n🔍 网络连接测试...")

    # 测试直连
    try:
        import requests
        print("   测试直连到 DeepSeek API...")
        response = requests.get("https://api.deepseek.com", timeout=10)
        print(f"   ✅ 直连成功 (状态码: {response.status_code})")
        direct_connection = True
    except Exception as e:
        print(f"   ❌ 直连失败: {str(e)}")
        direct_connection = False

    # 如果直连失败，尝试代理
    if not direct_connection:
        proxy_urls = [
            "http://127.0.0.1:10809",
            "http://127.0.0.1:7890",
            "http://127.0.0.1:1080",
            "socks5://127.0.0.1:10808"
        ]

        proxy_working = False
        for proxy_url in proxy_urls:
            try:
                print(f"   测试代理: {proxy_url}")
                proxies = {"http": proxy_url, "https": proxy_url}
                response = requests.get("https://api.deepseek.com",
                                      proxies=proxies, timeout=10)
                if response.status_code in [200, 404, 403]:  # 这些都表示连接成功
                    os.environ['HTTP_PROXY'] = proxy_url
                    os.environ['HTTPS_PROXY'] = proxy_url
                    print(f"   ✅ 代理连接成功: {proxy_url}")
                    proxy_working = True
                    break
            except Exception as e:
                print(f"   ❌ 代理 {proxy_url} 失败: {str(e)}")

        if not proxy_working:
            print("   ⚠️ 所有代理都无法连接，将使用直连模式")
            print("   💡 如果遇到连接问题，请检查:")
            print("      1. 网络连接是否正常")
            print("      2. 防火墙设置")
            print("      3. 代理软件是否运行")

    print(f"\n✅ API 端点: {DEEPSEEK_DIRECT_CONFIG['backend_url']}")
    print(f"✅ 模型: {DEEPSEEK_DIRECT_CONFIG['deep_think_llm']}")
    print(f"✅ 输出语言: 中文")



def main():
    """主函数"""
    # 创建全局控制台输出捕获器
    console_capture = ConsoleCapture()
    original_stdout = sys.stdout
    original_stderr = sys.stderr

    try:
        # 重定向stdout到捕获器
        sys.stdout = console_capture

        print("🔧 DeepSeek 中文金融分析系统")
        print("📋 使用说明:")
        print("   python run_deepseek_chinese.py [股票代码] [天数]")
        print("   示例: python run_deepseek_chinese.py TSLA 2")
        print("   默认: python run_deepseek_chinese.py AAPL 1")
        print()

        # 解析命令行参数
        if len(sys.argv) > 1:
            ticker = sys.argv[1].upper()
        else:
            ticker = "AAPL"  # 默认分析苹果股票

        if len(sys.argv) > 2:
            try:
                days_ago = int(sys.argv[2])
            except ValueError:
                print("⚠️ 天数参数无效，使用默认值 1")
                days_ago = 1
        else:
            days_ago = 1  # 默认分析昨天的数据

        print(f"📊 当前分析参数:")
        print(f"   股票代码: {ticker}")
        print(f"   分析日期: {days_ago} 天前")
        print(f"   输出语言: 中文")
        print(f"   API 类型: DeepSeek 直接 API")
        print()

        # 临时恢复stdout来调用分析函数
        sys.stdout = original_stdout
        success = run_chinese_analysis_with_capture(ticker, days_ago, console_capture)

        # 重新设置捕获
        sys.stdout = console_capture

        if success:
            print("\n🎉 DeepSeek 中文分析完成!")
            print("✨ 享受专业的中文金融分析报告!")
        else:
            print("\n❌ 分析失败!")
            print("💡 请检查错误信息并重试")

    except KeyboardInterrupt:
        print("\n\n⚠️ 程序被用户中断")
    except Exception as e:
        print(f"\n\n❌ 程序运行出现严重错误: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        # 恢复原始stdout
        sys.stdout = original_stdout
        sys.stderr = original_stderr

        # 保存完整的控制台输出到文件
        captured_output = console_capture.get_output()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 获取ticker，如果没有定义则使用默认值
        try:
            ticker_name = ticker
        except:
            ticker_name = "UNKNOWN"

        filename = f"deepseek_控制台输出_{ticker_name}_{timestamp}.txt"

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"DeepSeek 中文金融分析系统 - 完整控制台输出\n")
                f.write(f"{'='*60}\n")
                f.write(f"运行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"股票代码: {ticker_name}\n")
                f.write(f"API 端点: {DEEPSEEK_DIRECT_CONFIG['backend_url']}\n")
                f.write(f"使用模型: {DEEPSEEK_DIRECT_CONFIG['deep_think_llm']}\n")
                f.write(f"{'='*60}\n\n")
                f.write("=== 控制台输出内容 ===\n\n")
                f.write(captured_output)

            print(f"\n💾 完整控制台输出已保存到: {filename}")
        except Exception as e:
            print(f"\n❌ 保存控制台输出失败: {str(e)}")

def run_chinese_analysis_with_capture(ticker="AAPL", days_ago=1, console_capture=None):
    """运行中文分析（带控制台捕获）"""
    original_stdout = sys.stdout

    try:
        if console_capture:
            sys.stdout = console_capture

        # 设置环境
        setup_chinese_environment()

        # 计算分析日期
        target_date = datetime.now() - timedelta(days=days_ago)
        date_str = target_date.strftime("%Y-%m-%d")

        print(f"\n📊 分析设置:")
        print(f"   股票代码: {ticker}")
        print(f"   分析日期: {date_str}")
        print(f"   使用模型: {DEEPSEEK_DIRECT_CONFIG['deep_think_llm']}")
        print(f"   输出语言: 中文")
        print(f"   分析师团队: 市场分析师、社交媒体分析师、新闻分析师、基础面分析师")

        # 添加中文提示到配置
        chinese_config = DEEPSEEK_DIRECT_CONFIG.copy()
        chinese_config.update({
            "system_message": "你是一个专业的中文金融分析师。请用中文进行所有分析和回答。",
            "language_preference": "zh-CN"
        })

        try:
            print(f"\n🔄 正在初始化 DeepSeek 中文分析系统...")
            ta = TradingAgentsGraph(
                debug=True,
                config=chinese_config,
                selected_analysts=["market", "social", "news", "fundamentals"]
            )
            print("✅ 系统初始化成功!")

            print(f"\n🤖 DeepSeek 开始中文深度分析 {ticker} (日期: {date_str})...")
            print("⏳ 正在调用 DeepSeek 官方 API，生成中文分析报告...")
            print("   预计需要 3-5 分钟时间...")

            # 执行分析
            state, decision = ta.propagate(ticker, date_str)

            print("\n" + "="*70)
            print("🎯 DeepSeek 中文分析报告")
            print("="*70)
            print(decision)
            print("="*70)

            return True

        except Exception as e:
            print(f"\n❌ 分析过程中出现错误: {str(e)}")
            import traceback
            traceback.print_exc()

            error_msg = str(e).lower()
            error_type = type(e).__name__

            print(f"\n🔍 错误类型: {error_type}")

            if "connection" in error_msg or "apiconnectionerror" in error_type.lower():
                print("\n💡 网络连接问题解决方案:")
                print("1. 检查网络连接是否稳定")
                print("2. 尝试重启网络连接")
                print("3. 检查防火墙设置")
                print("4. 如果在中国大陆，尝试使用代理:")
                print("   - 启动代理软件 (如 Clash, V2Ray 等)")
                print("   - 确保代理端口正确 (常见: 7890, 10809)")
                print("5. 等待几分钟后重试")
                print("6. 检查 DeepSeek API 服务状态")

            elif "timeout" in error_msg:
                print("\n💡 超时问题解决方案:")
                print("1. 网络连接可能较慢，请稍后重试")
                print("2. 检查代理设置是否正确")
                print("3. 尝试更换网络环境")

            elif "api" in error_msg or "key" in error_msg or "unauthorized" in error_msg:
                print("\n💡 API 相关问题解决方案:")
                print("1. 检查 DeepSeek API 密钥是否正确")
                print("2. 确认 DeepSeek 账户余额充足")
                print("3. 访问 https://platform.deepseek.com/ 检查账户状态")
                print("4. 确认 API 密钥有效期")

            elif "rate" in error_msg or "limit" in error_msg:
                print("\n💡 速率限制解决方案:")
                print("1. 等待 5-10 分钟后重试")
                print("2. 检查 API 调用频率")
                print("3. 考虑升级 DeepSeek 账户套餐")

            elif "remoteprotocolerror" in error_type.lower() or "peer closed" in error_msg:
                print("\n💡 协议错误解决方案:")
                print("1. 这通常是网络不稳定导致的")
                print("2. 请检查网络连接质量")
                print("3. 如果使用代理，尝试更换代理节点")
                print("4. 等待几分钟后重试")
                print("5. 尝试重启代理软件")

            else:
                print("\n💡 通用解决方案:")
                print("1. 检查网络连接")
                print("2. 确认所有配置正确")
                print("3. 等待几分钟后重试")
                print("4. 查看完整错误日志以获取更多信息")

            return False

    finally:
        sys.stdout = original_stdout

if __name__ == "__main__":
    main()
