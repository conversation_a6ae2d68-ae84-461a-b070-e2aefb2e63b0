#!/usr/bin/env python3
"""
DeepSeek 中文版运行脚本 - 稳定版
专门处理网络连接问题，增强错误处理和重试机制
"""

import os
import sys
import io
import time
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tradingagents.graph.trading_graph import TradingAgentsGraph
from deepseek_config import DEEPSEEK_DIRECT_CONFIG

# 用于捕获控制台输出的类
class ConsoleCapture:
    def __init__(self):
        self.captured_output = []
        self.original_stdout = sys.stdout
        self.original_stderr = sys.stderr
        
    def write(self, text):
        # 同时输出到控制台和捕获
        self.original_stdout.write(text)
        self.original_stdout.flush()
        self.captured_output.append(text)
        
    def flush(self):
        self.original_stdout.flush()
        
    def get_output(self):
        return ''.join(self.captured_output)

def test_deepseek_connection():
    """测试 DeepSeek API 连接"""
    print("🔍 测试 DeepSeek API 连接...")
    
    try:
        import requests
        response = requests.get("https://api.deepseek.com", timeout=10)
        print(f"✅ DeepSeek API 可访问 (状态码: {response.status_code})")
        return True
    except Exception as e:
        print(f"❌ DeepSeek API 连接测试失败: {str(e)}")
        return False

def setup_chinese_environment():
    """设置中文环境"""
    print("🚀 DeepSeek 中文金融分析系统 - 稳定版")
    print("=" * 60)
    
    # 设置 DeepSeek API 密钥
    api_key = "***********************************"
    os.environ['OPENAI_API_KEY'] = api_key
    print(f"✅ 已设置 DeepSeek API 密钥: {api_key[:8]}...")
    
    # 设置 FinnHub API 密钥
    os.environ['FINNHUB_API_KEY'] = "d1bu1hhr01qsbpuf9dugd1bu1hhr01qsbpuf9dv0"
    print("✅ 已设置 FinnHub API 密钥")
    
    # 测试网络连接
    if not test_deepseek_connection():
        print("⚠️ DeepSeek API 连接测试失败，但将继续尝试运行")
    
    print(f"✅ API 端点: {DEEPSEEK_DIRECT_CONFIG['backend_url']}")
    print(f"✅ 模型: {DEEPSEEK_DIRECT_CONFIG['deep_think_llm']}")
    print(f"✅ 输出语言: 中文")

def run_analysis_with_retry(ticker="AAPL", days_ago=1, max_retries=3):
    """带重试机制的分析函数"""
    
    # 设置环境
    setup_chinese_environment()
    
    # 计算分析日期
    target_date = datetime.now() - timedelta(days=days_ago)
    date_str = target_date.strftime("%Y-%m-%d")
    
    print(f"\n📊 分析设置:")
    print(f"   股票代码: {ticker}")
    print(f"   分析日期: {date_str}")
    print(f"   使用模型: {DEEPSEEK_DIRECT_CONFIG['deep_think_llm']}")
    print(f"   输出语言: 中文")
    print(f"   最大重试次数: {max_retries}")
    print(f"   分析师团队: 市场分析师、社交媒体分析师、新闻分析师、基础面分析师")
    
    # 添加中文提示到配置
    chinese_config = DEEPSEEK_DIRECT_CONFIG.copy()
    chinese_config.update({
        "system_message": "你是一个专业的中文金融分析师。请用中文进行所有分析和回答。",
        "language_preference": "zh-CN",
        # 减少对外部数据源的依赖，避免速率限制
        "online_tools": False,  # 暂时禁用在线工具
        "use_cached_data": True,  # 优先使用缓存数据
        "fallback_analysis": True  # 启用备用分析模式
    })
    
    for attempt in range(max_retries):
        try:
            print(f"\n🔄 第 {attempt + 1} 次尝试 - 正在初始化 DeepSeek 中文分析系统...")
            
            ta = TradingAgentsGraph(
                debug=True, 
                config=chinese_config,
                selected_analysts=["market", "social", "news", "fundamentals"]
            )
            print("✅ 系统初始化成功!")
            
            print(f"\n🤖 DeepSeek 开始中文深度分析 {ticker} (日期: {date_str})...")
            print("⏳ 正在调用 DeepSeek 官方 API，生成中文分析报告...")
            print("   预计需要 3-5 分钟时间...")
            
            # 执行分析
            state, decision = ta.propagate(ticker, date_str)
            
            print("\n" + "="*70)
            print("🎯 DeepSeek 中文分析报告")
            print("="*70)
            print(decision)
            print("="*70)
            
            print(f"\n✅ 分析成功完成! (第 {attempt + 1} 次尝试)")
            return True, decision
            
        except Exception as e:
            error_msg = str(e)
            error_type = type(e).__name__
            
            print(f"\n❌ 第 {attempt + 1} 次尝试失败: {error_msg}")
            print(f"🔍 错误类型: {error_type}")
            
            # 分析错误类型并给出建议
            if "connection" in error_msg.lower() or "remoteprotocolerror" in error_type.lower():
                print("💡 这是网络连接问题")
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 10  # 递增等待时间
                    print(f"⏳ 等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    print("💡 网络连接问题解决建议:")
                    print("1. 检查网络连接是否稳定")
                    print("2. 尝试重启网络连接")
                    print("3. 检查防火墙设置")
                    print("4. 稍后再试")
                    
            elif "timeout" in error_msg.lower():
                print("💡 这是超时问题")
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 15  # 超时问题等待更长时间
                    print(f"⏳ 等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                    
            elif "rate" in error_msg.lower() or "limit" in error_msg.lower():
                print("💡 这是速率限制问题")
                if attempt < max_retries - 1:
                    wait_time = 60  # 速率限制等待1分钟
                    print(f"⏳ 等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                    
            else:
                print("💡 未知错误类型")
                if attempt < max_retries - 1:
                    wait_time = 30
                    print(f"⏳ 等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
            
            # 打印详细错误信息
            if attempt == max_retries - 1:  # 最后一次尝试失败
                print("\n📋 详细错误信息:")
                import traceback
                traceback.print_exc()
    
    print(f"\n❌ 所有 {max_retries} 次尝试都失败了")
    return False, None

def main():
    """主函数"""
    # 创建控制台输出捕获器
    console_capture = ConsoleCapture()
    original_stdout = sys.stdout
    
    try:
        # 重定向stdout到捕获器
        sys.stdout = console_capture
        
        print("🔧 DeepSeek 中文金融分析系统 - 稳定版")
        print("📋 使用说明:")
        print("   python run_deepseek_chinese_stable.py [股票代码] [天数]")
        print("   示例: python run_deepseek_chinese_stable.py TSLA 2")
        print("   默认: python run_deepseek_chinese_stable.py AAPL 1")
        print()
        
        # 解析命令行参数
        if len(sys.argv) > 1:
            ticker = sys.argv[1].upper()
        else:
            ticker = "AAPL"  # 默认分析苹果股票
        
        if len(sys.argv) > 2:
            try:
                days_ago = int(sys.argv[2])
            except ValueError:
                print("⚠️ 天数参数无效，使用默认值 1")
                days_ago = 1
        else:
            days_ago = 1  # 默认分析昨天的数据
        
        print(f"📊 当前分析参数:")
        print(f"   股票代码: {ticker}")
        print(f"   分析日期: {days_ago} 天前")
        print(f"   输出语言: 中文")
        print(f"   API 类型: DeepSeek 直接 API (稳定版)")
        print()
        
        # 临时恢复stdout来调用分析函数
        sys.stdout = original_stdout
        success, decision = run_analysis_with_retry(ticker, days_ago)
        
        # 重新设置捕获
        sys.stdout = console_capture
        
        if success:
            print("\n🎉 DeepSeek 中文分析完成!")
            print("✨ 享受专业的中文金融分析报告!")
        else:
            print("\n❌ 分析失败!")
            print("💡 请检查网络连接后重试")
        
    except KeyboardInterrupt:
        print("\n\n⚠️ 程序被用户中断")
    except Exception as e:
        print(f"\n\n❌ 程序运行出现严重错误: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        # 恢复原始stdout
        sys.stdout = original_stdout
        
        # 保存完整的控制台输出到文件
        captured_output = console_capture.get_output()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 获取ticker，如果没有定义则使用默认值
        try:
            ticker_name = ticker
        except:
            ticker_name = "UNKNOWN"
            
        filename = f"deepseek_稳定版输出_{ticker_name}_{timestamp}.txt"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"DeepSeek 中文金融分析系统 - 稳定版完整输出\n")
                f.write(f"{'='*60}\n")
                f.write(f"运行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"股票代码: {ticker_name}\n")
                f.write(f"API 端点: {DEEPSEEK_DIRECT_CONFIG['backend_url']}\n")
                f.write(f"使用模型: {DEEPSEEK_DIRECT_CONFIG['deep_think_llm']}\n")
                f.write(f"{'='*60}\n\n")
                f.write("=== 控制台输出内容 ===\n\n")
                f.write(captured_output)
            
            print(f"\n💾 完整控制台输出已保存到: {filename}")
        except Exception as e:
            print(f"\n❌ 保存控制台输出失败: {str(e)}")

if __name__ == "__main__":
    main()
