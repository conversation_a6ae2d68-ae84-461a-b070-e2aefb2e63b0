#!/usr/bin/env python3
"""
DeepSeek 直接 API 运行脚本
直接使用 DeepSeek 官方 API 进行金融交易分析
"""

import os
import sys
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tradingagents.graph.trading_graph import TradingAgentsGraph
from deepseek_config import DEEPSEEK_DIRECT_CONFIG

def setup_deepseek_direct():
    """设置 DeepSeek 直接 API 环境"""
    print("🚀 DeepSeek 直接 API 金融交易分析系统")
    print("=" * 50)
    
    # 设置 DeepSeek API 密钥
    api_key = "***********************************"  # 你的 DeepSeek API 密钥
    
    if api_key and api_key != "your_deepseek_api_key_here":
        os.environ['OPENAI_API_KEY'] = api_key
        print(f"✅ 已设置 DeepSeek API 密钥: {api_key[:8]}...")
    else:
        print("⚠️ 请设置你的 DeepSeek API 密钥")
        api_key = input("请输入 DeepSeek API 密钥: ").strip()
        if api_key:
            os.environ['OPENAI_API_KEY'] = api_key
            print(f"✅ 已设置 API 密钥: {api_key[:8]}...")
        else:
            print("❌ 未设置 API 密钥，无法继续")
            sys.exit(1)
    
    # 设置其他必要的 API 密钥
    os.environ['FINNHUB_API_KEY'] = "d1bu1hhr01qsbpuf9dugd1bu1hhr01qsbpuf9dv0"
    print("✅ 已设置 FinnHub API 密钥")
    
    # 检查是否需要代理
    proxy_needed = input("是否需要设置代理访问 DeepSeek API? (y/N): ").strip().lower()
    if proxy_needed == 'y':
        proxy_url = input("请输入代理地址 (例: http://127.0.0.1:10809): ").strip()
        if proxy_url:
            os.environ['HTTP_PROXY'] = proxy_url
            os.environ['HTTPS_PROXY'] = proxy_url
            print(f"✅ 代理已设置: {proxy_url}")
    
    print(f"✅ 使用 DeepSeek 直接 API: {DEEPSEEK_DIRECT_CONFIG['backend_url']}")
    print(f"✅ 模型: {DEEPSEEK_DIRECT_CONFIG['deep_think_llm']}")

def get_analysis_params():
    """获取分析参数"""
    print("\n📈 分析参数设置:")
    
    # 股票代码
    ticker = input("请输入股票代码 (例: AAPL, TSLA, NVDA, 默认 AAPL): ").strip().upper()
    if not ticker:
        ticker = "AAPL"
    print(f"股票代码: {ticker}")
    
    # 分析日期
    days_ago = input("分析几天前的数据? (默认 1): ").strip()
    try:
        days_ago = int(days_ago) if days_ago else 1
    except ValueError:
        days_ago = 1
    
    target_date = datetime.now() - timedelta(days=days_ago)
    date_str = target_date.strftime("%Y-%m-%d")
    print(f"分析日期: {date_str}")
    
    return ticker, date_str

def run_deepseek_analysis():
    """运行 DeepSeek 分析"""
    
    # 设置环境
    setup_deepseek_direct()
    
    # 获取分析参数
    ticker, date_str = get_analysis_params()
    
    print(f"\n🤖 分析师团队:")
    print("- 市场分析师 (market)")
    print("- 社交媒体分析师 (social)")  
    print("- 新闻分析师 (news)")
    print("- 基础面分析师 (fundamentals)")
    
    try:
        print(f"\n🔄 正在初始化 DeepSeek 交易分析系统...")
        print(f"   API 端点: {DEEPSEEK_DIRECT_CONFIG['backend_url']}")
        print(f"   模型: {DEEPSEEK_DIRECT_CONFIG['deep_think_llm']}")
        
        ta = TradingAgentsGraph(
            debug=True, 
            config=DEEPSEEK_DIRECT_CONFIG,
            selected_analysts=["market", "social", "news", "fundamentals"]
        )
        print("✅ 系统初始化成功!")
        
        print(f"\n📊 开始分析 {ticker} (日期: {date_str})...")
        print("⏳ DeepSeek 正在深度思考，请耐心等待...")
        print("   这可能需要 3-5 分钟时间...")
        
        # 执行分析
        state, decision = ta.propagate(ticker, date_str)
        
        print("\n" + "="*60)
        print("🎯 DeepSeek 分析结果")
        print("="*60)
        print(decision)
        print("="*60)
        
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"deepseek_direct_analysis_{ticker}_{timestamp}.txt"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"DeepSeek 直接 API 金融分析报告\n")
            f.write(f"API 端点: {DEEPSEEK_DIRECT_CONFIG['backend_url']}\n")
            f.write(f"模型: {DEEPSEEK_DIRECT_CONFIG['deep_think_llm']}\n")
            f.write(f"股票代码: {ticker}\n")
            f.write(f"分析日期: {date_str}\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("\n" + "="*60 + "\n")
            f.write(decision)
        
        print(f"\n💾 分析结果已保存到: {filename}")
        print("\n🎉 DeepSeek 分析完成!")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 分析过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        
        if "API" in str(e) or "key" in str(e).lower():
            print("\n💡 可能的解决方案:")
            print("1. 检查 DeepSeek API 密钥是否正确")
            print("2. 确认 DeepSeek 账户余额充足")
            print("3. 检查网络连接和代理设置")
            print("4. 访问 https://platform.deepseek.com/ 获取 API 密钥")
            print("5. 确认 API 密钥有访问权限")
        elif "rate" in str(e).lower() or "limit" in str(e).lower():
            print("\n💡 遇到速率限制:")
            print("1. 等待几分钟后重试")
            print("2. 检查 API 调用频率")
            print("3. 升级 DeepSeek 账户套餐")
        
        return False

def main():
    """主函数"""
    print("🔧 DeepSeek 直接 API 配置说明:")
    print("1. 使用 DeepSeek 官方 API (https://api.deepseek.com/v1)")
    print("2. 不通过 OpenRouter 等第三方服务")
    print("3. 需要 DeepSeek 官方 API 密钥")
    print("4. 支持最新的 deepseek-chat 模型")
    print()
    
    success = run_deepseek_analysis()
    
    if success:
        print("\n✨ 使用 DeepSeek 直接 API 分析成功完成!")
    else:
        print("\n❌ 分析失败，请检查配置和网络连接")
        sys.exit(1)

if __name__ == "__main__":
    main()
