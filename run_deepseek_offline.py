#!/usr/bin/env python3
"""
DeepSeek 离线分析版本
专门处理 API 速率限制问题，减少对外部数据源的依赖
"""

import os
import sys
import io
import time
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tradingagents.graph.trading_graph import TradingAgentsGraph
from deepseek_config import DEEPSEEK_DIRECT_CONFIG

# 用于捕获控制台输出的类
class ConsoleCapture:
    def __init__(self):
        self.captured_output = []
        self.original_stdout = sys.stdout
        self.original_stderr = sys.stderr
        
    def write(self, text):
        # 同时输出到控制台和捕获
        self.original_stdout.write(text)
        self.original_stdout.flush()
        self.captured_output.append(text)
        
    def flush(self):
        self.original_stdout.flush()
        
    def get_output(self):
        return ''.join(self.captured_output)

def setup_offline_environment():
    """设置离线分析环境"""
    print("🚀 DeepSeek 离线金融分析系统")
    print("=" * 60)
    print("💡 此版本减少对外部数据源的依赖，避免速率限制问题")
    
    # 设置 DeepSeek API 密钥
    api_key = "***********************************"
    os.environ['OPENAI_API_KEY'] = api_key
    print(f"✅ 已设置 DeepSeek API 密钥: {api_key[:8]}...")
    
    # 设置 FinnHub API 密钥（备用）
    os.environ['FINNHUB_API_KEY'] = "d1bu1hhr01qsbpuf9dugd1bu1hhr01qsbpuf9dv0"
    print("✅ 已设置 FinnHub API 密钥（备用）")
    
    print(f"✅ API 端点: {DEEPSEEK_DIRECT_CONFIG['backend_url']}")
    print(f"✅ 模型: {DEEPSEEK_DIRECT_CONFIG['deep_think_llm']}")
    print(f"✅ 输出语言: 中文")
    print("⚠️ 离线模式：减少外部数据获取，主要基于 AI 分析")

def create_offline_analysis_prompt(ticker, date_str):
    """创建离线分析提示"""
    return f"""
请作为专业的中文金融分析师，对股票 {ticker} 进行全面分析（分析日期：{date_str}）。

由于当前无法获取实时数据，请基于您的知识库进行分析，包括：

## 📊 技术分析
- 基于历史趋势的技术指标分析
- 支撑位和阻力位预测
- 移动平均线趋势分析
- 相对强弱指数(RSI)评估

## 📰 基本面分析  
- 公司基本情况和业务模式
- 行业地位和竞争优势
- 财务健康状况评估
- 近期重要事件影响

## 🌍 市场环境分析
- 宏观经济环境影响
- 行业发展趋势
- 市场情绪和投资者关注点
- 政策环境影响

## 💡 投资建议
- 综合评估和投资建议
- 风险提示和注意事项
- 建议持有期和目标价位
- 适合的投资者类型

请用中文详细分析，并在最后给出明确的投资建议（买入/持有/卖出）。
"""

def run_offline_analysis(ticker="AAPL", days_ago=1):
    """运行离线分析"""
    
    # 设置环境
    setup_offline_environment()
    
    # 计算分析日期
    target_date = datetime.now() - timedelta(days=days_ago)
    date_str = target_date.strftime("%Y-%m-%d")
    
    print(f"\n📊 离线分析设置:")
    print(f"   股票代码: {ticker}")
    print(f"   分析日期: {date_str}")
    print(f"   使用模型: {DEEPSEEK_DIRECT_CONFIG['deep_think_llm']}")
    print(f"   输出语言: 中文")
    print(f"   分析模式: 离线模式（基于AI知识库）")
    
    # 创建离线配置
    offline_config = DEEPSEEK_DIRECT_CONFIG.copy()
    offline_config.update({
        "system_message": "你是一个专业的中文金融分析师。请用中文进行所有分析和回答。",
        "language_preference": "zh-CN",
        "online_tools": False,  # 禁用在线工具
        "max_debate_rounds": 1,  # 减少轮数以避免过多API调用
        "max_risk_discuss_rounds": 1
    })
    
    try:
        print(f"\n🔄 正在初始化 DeepSeek 离线分析系统...")
        
        # 使用简化的分析方式
        from openai import OpenAI
        
        client = OpenAI(
            api_key=offline_config.get('api_key', os.environ.get('OPENAI_API_KEY')),
            base_url=offline_config['backend_url']
        )
        
        print("✅ 系统初始化成功!")
        
        print(f"\n🤖 DeepSeek 开始离线分析 {ticker} (日期: {date_str})...")
        print("⏳ 正在生成基于AI知识库的分析报告...")
        print("   预计需要 1-2 分钟时间...")
        
        # 创建分析提示
        analysis_prompt = create_offline_analysis_prompt(ticker, date_str)
        
        # 执行分析
        response = client.chat.completions.create(
            model=offline_config['deep_think_llm'],
            messages=[
                {"role": "system", "content": offline_config['system_message']},
                {"role": "user", "content": analysis_prompt}
            ],
            max_tokens=4000,
            temperature=0.7
        )
        
        decision = response.choices[0].message.content
        
        print("\n" + "="*70)
        print("🎯 DeepSeek 离线分析报告")
        print("="*70)
        print(decision)
        print("="*70)
        
        print(f"\n✅ 离线分析成功完成!")
        return True, decision
        
    except Exception as e:
        error_msg = str(e)
        error_type = type(e).__name__
        
        print(f"\n❌ 离线分析失败: {error_msg}")
        print(f"🔍 错误类型: {error_type}")
        
        if "connection" in error_msg.lower():
            print("💡 网络连接问题解决建议:")
            print("1. 检查网络连接是否稳定")
            print("2. 尝试重启网络连接")
            print("3. 检查防火墙设置")
            
        elif "timeout" in error_msg.lower():
            print("💡 超时问题解决建议:")
            print("1. 网络连接可能较慢，请稍后重试")
            print("2. 检查代理设置是否正确")
            
        elif "api" in error_msg.lower() or "unauthorized" in error_msg.lower():
            print("💡 API 问题解决建议:")
            print("1. 检查 DeepSeek API 密钥是否正确")
            print("2. 确认 DeepSeek 账户余额充足")
            print("3. 访问 https://platform.deepseek.com/ 检查账户状态")
            
        else:
            print("💡 通用解决建议:")
            print("1. 等待几分钟后重试")
            print("2. 检查所有配置是否正确")
        
        # 打印详细错误信息
        print("\n📋 详细错误信息:")
        import traceback
        traceback.print_exc()
        
        return False, None

def main():
    """主函数"""
    # 创建控制台输出捕获器
    console_capture = ConsoleCapture()
    original_stdout = sys.stdout
    
    try:
        # 重定向stdout到捕获器
        sys.stdout = console_capture
        
        print("🔧 DeepSeek 离线金融分析系统")
        print("📋 使用说明:")
        print("   python run_deepseek_offline.py [股票代码] [天数]")
        print("   示例: python run_deepseek_offline.py TSLA 2")
        print("   默认: python run_deepseek_offline.py AAPL 1")
        print()
        print("💡 此版本专门解决 API 速率限制问题")
        print("   - 不依赖 Yahoo Finance 等外部数据源")
        print("   - 基于 DeepSeek AI 知识库进行分析")
        print("   - 避免 'Too Many Requests' 错误")
        print()
        
        # 解析命令行参数
        if len(sys.argv) > 1:
            ticker = sys.argv[1].upper()
        else:
            ticker = "AAPL"  # 默认分析苹果股票
        
        if len(sys.argv) > 2:
            try:
                days_ago = int(sys.argv[2])
            except ValueError:
                print("⚠️ 天数参数无效，使用默认值 1")
                days_ago = 1
        else:
            days_ago = 1  # 默认分析昨天的数据
        
        print(f"📊 当前分析参数:")
        print(f"   股票代码: {ticker}")
        print(f"   分析日期: {days_ago} 天前")
        print(f"   输出语言: 中文")
        print(f"   分析模式: 离线模式")
        print()
        
        # 临时恢复stdout来调用分析函数
        sys.stdout = original_stdout
        success, decision = run_offline_analysis(ticker, days_ago)
        
        # 重新设置捕获
        sys.stdout = console_capture
        
        if success:
            print("\n🎉 DeepSeek 离线分析完成!")
            print("✨ 享受基于AI知识库的专业分析报告!")
        else:
            print("\n❌ 分析失败!")
            print("💡 请检查网络连接和API配置后重试")
        
    except KeyboardInterrupt:
        print("\n\n⚠️ 程序被用户中断")
    except Exception as e:
        print(f"\n\n❌ 程序运行出现严重错误: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        # 恢复原始stdout
        sys.stdout = original_stdout
        
        # 保存完整的控制台输出到文件
        captured_output = console_capture.get_output()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 获取ticker，如果没有定义则使用默认值
        try:
            ticker_name = ticker
        except:
            ticker_name = "UNKNOWN"
            
        filename = f"deepseek_离线分析_{ticker_name}_{timestamp}.txt"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"DeepSeek 离线金融分析系统 - 完整输出\n")
                f.write(f"{'='*60}\n")
                f.write(f"运行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"股票代码: {ticker_name}\n")
                f.write(f"分析模式: 离线模式（避免速率限制）\n")
                f.write(f"API 端点: {DEEPSEEK_DIRECT_CONFIG['backend_url']}\n")
                f.write(f"使用模型: {DEEPSEEK_DIRECT_CONFIG['deep_think_llm']}\n")
                f.write(f"{'='*60}\n\n")
                f.write("=== 控制台输出内容 ===\n\n")
                f.write(captured_output)
            
            print(f"\n💾 完整控制台输出已保存到: {filename}")
        except Exception as e:
            print(f"\n❌ 保存控制台输出失败: {str(e)}")

if __name__ == "__main__":
    main()
