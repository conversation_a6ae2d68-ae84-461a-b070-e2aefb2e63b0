#!/usr/bin/env python3
"""
测试 DeepSeek 直接 API 连接
"""

import os
import sys
import requests
from openai import OpenAI

def test_deepseek_api():
    """测试 DeepSeek API 连接"""
    print("🧪 测试 DeepSeek 直接 API 连接")
    print("=" * 50)
    
    # 设置 API 密钥
    api_key = "sk-3dc3163acbba4985ad492657b6bba7d5"
    os.environ['OPENAI_API_KEY'] = api_key
    print(f"✅ API 密钥: {api_key[:8]}...")
    
    # 设置代理（如果需要）
    proxy_url = "http://127.0.0.1:10809"
    try:
        # 测试代理连接
        response = requests.get("https://httpbin.org/ip", 
                              proxies={"http": proxy_url, "https": proxy_url}, 
                              timeout=5)
        if response.status_code == 200:
            os.environ['HTTP_PROXY'] = proxy_url
            os.environ['HTTPS_PROXY'] = proxy_url
            print(f"✅ 代理设置: {proxy_url}")
        else:
            print("⚠️ 代理测试失败，使用直连")
    except:
        print("⚠️ 代理不可用，使用直连")
    
    # 测试 DeepSeek API
    try:
        print("\n🔄 测试 DeepSeek API...")
        
        client = OpenAI(
            api_key=api_key,
            base_url="https://api.deepseek.com/v1"
        )
        
        # 发送简单的测试请求
        response = client.chat.completions.create(
            model="deepseek-chat",
            messages=[
                {"role": "user", "content": "你好，请简单介绍一下你自己。"}
            ],
            max_tokens=100,
            temperature=0.7
        )
        
        print("✅ DeepSeek API 连接成功!")
        print(f"📝 响应: {response.choices[0].message.content}")
        
        return True
        
    except Exception as e:
        print(f"❌ DeepSeek API 测试失败: {str(e)}")
        
        error_msg = str(e).lower()
        if "api" in error_msg or "key" in error_msg:
            print("\n💡 API 密钥问题:")
            print("1. 检查 DeepSeek API 密钥是否正确")
            print("2. 访问 https://platform.deepseek.com/ 获取密钥")
            print("3. 确认账户余额充足")
        elif "connection" in error_msg or "timeout" in error_msg:
            print("\n💡 网络连接问题:")
            print("1. 检查网络连接")
            print("2. 尝试设置代理")
            print("3. 检查防火墙设置")
        elif "rate" in error_msg or "limit" in error_msg:
            print("\n💡 速率限制问题:")
            print("1. 等待几分钟后重试")
            print("2. 检查 API 调用频率")
        
        return False

def test_network_connectivity():
    """测试网络连接"""
    print("\n🌐 测试网络连接...")
    
    test_urls = [
        "https://httpbin.org/ip",
        "https://api.deepseek.com",
        "https://www.baidu.com"
    ]
    
    for url in test_urls:
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                print(f"✅ {url} - 连接成功")
            else:
                print(f"⚠️ {url} - 状态码: {response.status_code}")
        except Exception as e:
            print(f"❌ {url} - 连接失败: {str(e)}")

def main():
    """主函数"""
    print("🔧 DeepSeek 直接 API 测试工具")
    print("用于验证 DeepSeek 官方 API 连接是否正常")
    print()
    
    # 测试网络连接
    test_network_connectivity()
    
    # 测试 DeepSeek API
    success = test_deepseek_api()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 DeepSeek 直接 API 测试成功!")
        print("现在可以运行完整的分析程序:")
        print("   python run_deepseek_direct.py")
        print("   或")
        print("   python run_auto_deepseek.py")
    else:
        print("❌ DeepSeek API 测试失败")
        print("请检查上述错误信息并解决问题")
    
    print("\n📋 DeepSeek 直接 API 配置信息:")
    print("- API 端点: https://api.deepseek.com/v1")
    print("- 模型: deepseek-chat")
    print("- 兼容: OpenAI API 格式")

if __name__ == "__main__":
    main()
