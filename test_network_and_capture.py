#!/usr/bin/env python3
"""
网络连接测试和控制台输出捕获测试脚本
用于诊断网络问题并测试控制台输出捕获功能
"""

import os
import sys
import io
import time
from datetime import datetime, timedelta

# 用于捕获控制台输出的类
class ConsoleCapture:
    def __init__(self):
        self.captured_output = []
        self.original_stdout = sys.stdout
        self.original_stderr = sys.stderr
        
    def write(self, text):
        # 同时输出到控制台和捕获
        self.original_stdout.write(text)
        self.original_stdout.flush()
        self.captured_output.append(text)
        
    def flush(self):
        self.original_stdout.flush()
        
    def get_output(self):
        return ''.join(self.captured_output)

def test_network_connection():
    """测试网络连接"""
    print("🔍 开始网络连接测试...")
    print("=" * 50)
    
    import requests
    
    # 测试基本网络连接
    test_urls = [
        ("百度", "https://www.baidu.com"),
        ("Google", "https://www.google.com"),
        ("DeepSeek API", "https://api.deepseek.com"),
        ("OpenAI API", "https://api.openai.com")
    ]
    
    print("\n📡 测试直连:")
    for name, url in test_urls:
        try:
            print(f"   测试 {name} ({url})...")
            response = requests.get(url, timeout=10)
            print(f"   ✅ {name} 连接成功 (状态码: {response.status_code})")
        except Exception as e:
            print(f"   ❌ {name} 连接失败: {str(e)}")
    
    # 测试代理连接
    proxy_configs = [
        ("HTTP代理 7890", {"http": "http://127.0.0.1:7890", "https": "http://127.0.0.1:7890"}),
        ("HTTP代理 10809", {"http": "http://127.0.0.1:10809", "https": "http://127.0.0.1:10809"}),
        ("SOCKS5代理 10808", {"http": "socks5://127.0.0.1:10808", "https": "socks5://127.0.0.1:10808"}),
    ]
    
    print("\n🔄 测试代理连接:")
    for proxy_name, proxy_config in proxy_configs:
        try:
            print(f"   测试 {proxy_name}...")
            response = requests.get("https://api.deepseek.com", 
                                  proxies=proxy_config, timeout=10)
            print(f"   ✅ {proxy_name} 连接成功 (状态码: {response.status_code})")
        except Exception as e:
            print(f"   ❌ {proxy_name} 连接失败: {str(e)}")

def test_deepseek_api():
    """测试 DeepSeek API 连接"""
    print("\n🤖 测试 DeepSeek API 连接...")
    print("=" * 50)
    
    try:
        from openai import OpenAI
        
        # 设置 API 密钥
        api_key = "sk-3dc3163acbba4985ad492657b6bba7d5"
        
        client = OpenAI(
            api_key=api_key,
            base_url="https://api.deepseek.com/v1"
        )
        
        print("   发送测试请求到 DeepSeek API...")
        
        response = client.chat.completions.create(
            model="deepseek-chat",
            messages=[
                {"role": "user", "content": "你好，请用中文回答：今天天气怎么样？"}
            ],
            max_tokens=50,
            timeout=30
        )
        
        print("   ✅ DeepSeek API 连接成功!")
        print(f"   📝 API 响应: {response.choices[0].message.content}")
        return True
        
    except Exception as e:
        print(f"   ❌ DeepSeek API 连接失败: {str(e)}")
        print(f"   🔍 错误类型: {type(e).__name__}")
        
        error_msg = str(e).lower()
        if "connection" in error_msg:
            print("   💡 这是网络连接问题，请检查:")
            print("      1. 网络连接是否正常")
            print("      2. 是否需要使用代理")
            print("      3. 防火墙设置")
        elif "unauthorized" in error_msg or "api" in error_msg:
            print("   💡 这是 API 认证问题，请检查:")
            print("      1. API 密钥是否正确")
            print("      2. 账户余额是否充足")
        
        return False

def simulate_analysis_output():
    """模拟分析输出过程"""
    print("\n📊 模拟分析过程...")
    print("=" * 50)
    
    print("🚀 DeepSeek 中文金融分析系统")
    print("✅ 已设置 DeepSeek API 密钥: sk-3dc31...")
    print("✅ 已设置 FinnHub API 密钥")
    
    print("\n📊 分析设置:")
    print("   股票代码: TEST")
    print("   分析日期: 2025-07-29")
    print("   使用模型: deepseek-chat")
    print("   输出语言: 中文")
    
    print("\n🔄 正在初始化系统...")
    time.sleep(1)
    print("✅ 系统初始化成功!")
    
    print("\n🤖 开始模拟分析...")
    print("⏳ 正在调用 API...")
    
    # 模拟一些分析步骤
    steps = [
        "📈 获取市场数据...",
        "📰 分析新闻情感...",
        "💬 处理社交媒体数据...",
        "📊 进行技术分析...",
        "🎯 生成投资建议..."
    ]
    
    for step in steps:
        print(f"   {step}")
        time.sleep(0.5)
    
    print("\n" + "="*70)
    print("🎯 模拟分析报告")
    print("="*70)
    print("这是一个模拟的分析报告内容。")
    print("在实际运行中，这里会包含详细的金融分析结果。")
    print("包括技术指标、基本面分析、市场情绪等信息。")
    print("="*70)

def main():
    """主函数"""
    # 创建控制台输出捕获器
    console_capture = ConsoleCapture()
    original_stdout = sys.stdout
    
    try:
        # 重定向stdout到捕获器
        sys.stdout = console_capture
        
        print("🔧 网络连接和控制台输出测试工具")
        print("=" * 60)
        print(f"运行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 执行各种测试
        test_network_connection()
        
        # 测试 DeepSeek API
        api_success = test_deepseek_api()
        
        # 模拟分析输出
        simulate_analysis_output()
        
        if api_success:
            print("\n🎉 所有测试完成! DeepSeek API 连接正常")
        else:
            print("\n⚠️ 测试完成，但 DeepSeek API 连接存在问题")
            print("💡 请根据上述错误信息进行网络配置调整")
        
    except KeyboardInterrupt:
        print("\n\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n\n❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        # 恢复原始stdout
        sys.stdout = original_stdout
        
        # 保存完整的控制台输出到文件
        captured_output = console_capture.get_output()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"网络测试输出_{timestamp}.txt"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"网络连接和控制台输出测试 - 完整输出\n")
                f.write(f"{'='*60}\n")
                f.write(f"运行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"{'='*60}\n\n")
                f.write("=== 控制台输出内容 ===\n\n")
                f.write(captured_output)
            
            print(f"\n💾 完整测试输出已保存到: {filename}")
        except Exception as e:
            print(f"\n❌ 保存测试输出失败: {str(e)}")

if __name__ == "__main__":
    main()
