import json
import numpy as np
from openai import OpenAI
from typing import List, Dict, Any


class FinancialSituationMemory:
    def __init__(self, name, config):
        if config["backend_url"] == "http://localhost:11434/v1":
            self.embedding = "nomic-embed-text"
            self.use_openai = False
        else:
            self.embedding = "text-embedding-3-small"
            self.client = OpenAI()
            self.use_openai = True

        # 使用简单的内存存储替代 ChromaDB
        self.name = name
        self.situations = []  # 存储情况文本
        self.recommendations = []  # 存储建议
        self.embeddings = []  # 存储嵌入向量

    def get_embedding(self, text):
        """Get embedding for a text"""
        if self.use_openai:
            response = self.client.embeddings.create(
                model=self.embedding, input=text
            )
            return response.data[0].embedding
        else:
            # 简单的文本向量化（用于本地模型）
            # 这里使用简单的词频向量作为示例
            words = text.lower().split()
            # 创建一个简单的词汇表
            vocab = set()
            for situation in self.situations:
                vocab.update(situation.lower().split())
            vocab.update(words)
            vocab = sorted(list(vocab))

            # 创建词频向量
            vector = [words.count(word) for word in vocab]
            # 归一化
            norm = np.linalg.norm(vector) if np.linalg.norm(vector) > 0 else 1
            return [v / norm for v in vector]

    def add_situations(self, situations_and_advice):
        """Add financial situations and their corresponding advice. Parameter is a list of tuples (situation, rec)"""
        for situation, recommendation in situations_and_advice:
            self.situations.append(situation)
            self.recommendations.append(recommendation)
            self.embeddings.append(self.get_embedding(situation))

    def cosine_similarity(self, vec1, vec2):
        """计算余弦相似度"""
        if len(vec1) != len(vec2):
            # 如果向量长度不同，填充较短的向量
            max_len = max(len(vec1), len(vec2))
            vec1 = vec1 + [0] * (max_len - len(vec1))
            vec2 = vec2 + [0] * (max_len - len(vec2))

        dot_product = sum(a * b for a, b in zip(vec1, vec2))
        norm1 = sum(a * a for a in vec1) ** 0.5
        norm2 = sum(b * b for b in vec2) ** 0.5

        if norm1 == 0 or norm2 == 0:
            return 0
        return dot_product / (norm1 * norm2)

    def get_memories(self, current_situation, n_matches=1):
        """Find matching recommendations using embeddings"""
        if not self.situations:
            return []

        query_embedding = self.get_embedding(current_situation)

        # 计算与所有存储情况的相似度
        similarities = []
        for i, stored_embedding in enumerate(self.embeddings):
            similarity = self.cosine_similarity(query_embedding, stored_embedding)
            similarities.append((similarity, i))

        # 按相似度排序并取前n个
        similarities.sort(reverse=True)
        top_matches = similarities[:n_matches]

        matched_results = []
        for similarity, idx in top_matches:
            matched_results.append({
                "matched_situation": self.situations[idx],
                "recommendation": self.recommendations[idx],
                "similarity_score": similarity,
            })

        return matched_results


if __name__ == "__main__":
    # Example usage
    config = {"backend_url": "http://localhost:11434/v1"}  # 或者其他配置
    matcher = FinancialSituationMemory("test_memory", config)

    # Example data
    example_data = [
        (
            "High inflation rate with rising interest rates and declining consumer spending",
            "Consider defensive sectors like consumer staples and utilities. Review fixed-income portfolio duration.",
        ),
        (
            "Tech sector showing high volatility with increasing institutional selling pressure",
            "Reduce exposure to high-growth tech stocks. Look for value opportunities in established tech companies with strong cash flows.",
        ),
        (
            "Strong dollar affecting emerging markets with increasing forex volatility",
            "Hedge currency exposure in international positions. Consider reducing allocation to emerging market debt.",
        ),
        (
            "Market showing signs of sector rotation with rising yields",
            "Rebalance portfolio to maintain target allocations. Consider increasing exposure to sectors benefiting from higher rates.",
        ),
    ]

    # Add the example situations and recommendations
    matcher.add_situations(example_data)

    # Example query
    current_situation = """
    Market showing increased volatility in tech sector, with institutional investors 
    reducing positions and rising interest rates affecting growth stock valuations
    """

    try:
        recommendations = matcher.get_memories(current_situation, n_matches=2)

        for i, rec in enumerate(recommendations, 1):
            print(f"\nMatch {i}:")
            print(f"Similarity Score: {rec['similarity_score']:.2f}")
            print(f"Matched Situation: {rec['matched_situation']}")
            print(f"Recommendation: {rec['recommendation']}")

    except Exception as e:
        print(f"Error during recommendation: {str(e)}")
