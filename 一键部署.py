#!/usr/bin/env python3
"""
TradingAgents DeepSeek 中文版一键部署脚本
自动完成所有部署步骤
"""

import os
import sys
import subprocess
import time

def print_step(step, description):
    """打印步骤信息"""
    print(f"\n{'='*60}")
    print(f"步骤 {step}: {description}")
    print('='*60)

def run_command(command, description=""):
    """运行命令并检查结果"""
    try:
        print(f"🔄 执行: {command}")
        result = subprocess.run(command, shell=True, capture_output=True, text=True, encoding='utf-8')
        if result.returncode == 0:
            print(f"✅ {description} 成功")
            if result.stdout:
                print(f"输出: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ {description} 失败")
            if result.stderr:
                print(f"错误: {result.stderr.strip()}")
            return False
    except Exception as e:
        print(f"❌ 执行命令时出错: {e}")
        return False

def check_python():
    """检查 Python 环境"""
    print_step(1, "检查 Python 环境")
    
    # 检查 Python 版本
    try:
        import sys
        version = sys.version_info
        if version.major >= 3 and version.minor >= 8:
            print(f"✅ Python 版本: {version.major}.{version.minor}.{version.micro}")
            return True
        else:
            print(f"❌ Python 版本过低: {version.major}.{version.minor}.{version.micro}")
            print("需要 Python 3.8 或更高版本")
            return False
    except Exception as e:
        print(f"❌ 检查 Python 版本失败: {e}")
        return False

def install_dependencies():
    """安装依赖包"""
    print_step(2, "安装依赖包")
    
    packages = [
        "numpy",
        "pandas", 
        "requests",
        "openai",
        "yfinance"
    ]
    
    success = True
    for package in packages:
        if not run_command(f"pip install {package}", f"安装 {package}"):
            success = False
    
    return success

def fix_chromadb():
    """修复 ChromaDB 问题"""
    print_step(3, "修复 ChromaDB 问题")
    
    if os.path.exists("fix_chromadb_issue.py"):
        return run_command("python fix_chromadb_issue.py", "修复 ChromaDB")
    else:
        print("⚠️ 修复脚本不存在，跳过此步骤")
        return True

def test_environment():
    """测试环境"""
    print_step(4, "测试环境")
    
    tests = [
        ("python -c \"import numpy, pandas, requests, openai; print('依赖包正常')\"", "测试依赖包"),
        ("python -c \"from tradingagents.agents.utils.memory import FinancialSituationMemory; print('ChromaDB 修复成功')\"", "测试 ChromaDB 修复"),
        ("python -c \"from deepseek_config import DEEPSEEK_DIRECT_CONFIG; print('配置文件正常')\"", "测试配置文件")
    ]
    
    success = True
    for command, description in tests:
        if not run_command(command, description):
            success = False
    
    return success

def test_api_connection():
    """测试 API 连接"""
    print_step(5, "测试 DeepSeek API 连接")
    
    if os.path.exists("test_deepseek_api.py"):
        return run_command("python test_deepseek_api.py", "测试 API 连接")
    else:
        print("⚠️ API 测试脚本不存在，跳过此步骤")
        return True

def run_sample_analysis():
    """运行示例分析"""
    print_step(6, "运行示例分析")
    
    print("🤖 即将运行 AAPL 股票的示例分析...")
    print("⏳ 这可能需要 3-5 分钟时间...")
    
    choice = input("是否运行示例分析? (y/N): ").strip().lower()
    if choice == 'y':
        if os.path.exists("run_deepseek_chinese.py"):
            print("🔄 开始运行中文分析...")
            return run_command("python run_deepseek_chinese.py AAPL 2", "示例分析")
        else:
            print("⚠️ 中文分析脚本不存在")
            return False
    else:
        print("⏭️ 跳过示例分析")
        return True

def show_usage_guide():
    """显示使用指南"""
    print_step(7, "使用指南")
    
    print("🎉 部署完成！以下是使用方法:")
    print()
    print("📋 可用命令:")
    print("   python run_deepseek_chinese.py              # 中文分析 AAPL")
    print("   python run_deepseek_chinese.py TSLA         # 中文分析特斯拉")
    print("   python run_deepseek_chinese.py NVDA 2       # 中文分析英伟达，2天前数据")
    print()
    print("   python run_auto_deepseek.py AAPL            # 英文分析")
    print("   python test_deepseek_api.py                 # 测试 API 连接")
    print()
    print("📊 热门股票代码:")
    print("   AAPL (苹果)    TSLA (特斯拉)   NVDA (英伟达)")
    print("   GOOGL (谷歌)   META (Meta)     MSFT (微软)")
    print("   AMZN (亚马逊)  NFLX (奈飞)    BABA (阿里巴巴)")
    print()
    print("📁 输出文件:")
    print("   deepseek_中文分析_AAPL_20250128_143022.txt")
    print()
    print("🔧 故障排除:")
    print("   python fix_chromadb_issue.py               # 修复 ChromaDB 问题")
    print("   python test_deepseek_api.py                # 测试连接")
    print()
    print("📖 详细文档: 完整部署文档.md")

def main():
    """主函数"""
    print("🚀 TradingAgents DeepSeek 中文版一键部署")
    print("=" * 60)
    print("这个脚本将自动完成所有部署步骤")
    print("包括: 环境检查、依赖安装、问题修复、连接测试")
    print()
    
    # 检查是否在正确目录
    if not os.path.exists("tradingagents"):
        print("❌ 错误: 请在项目根目录运行此脚本")
        print("当前目录应包含 'tradingagents' 文件夹")
        sys.exit(1)
    
    steps = [
        (check_python, "Python 环境检查"),
        (install_dependencies, "依赖包安装"),
        (fix_chromadb, "ChromaDB 问题修复"),
        (test_environment, "环境测试"),
        (test_api_connection, "API 连接测试"),
        (run_sample_analysis, "示例分析"),
        (show_usage_guide, "使用指南")
    ]
    
    failed_steps = []
    
    for i, (step_func, step_name) in enumerate(steps, 1):
        try:
            if not step_func():
                failed_steps.append(f"步骤 {i}: {step_name}")
        except KeyboardInterrupt:
            print("\n\n⚠️ 用户中断了部署过程")
            sys.exit(1)
        except Exception as e:
            print(f"\n❌ 步骤 {i} 执行时出错: {e}")
            failed_steps.append(f"步骤 {i}: {step_name}")
    
    # 总结
    print("\n" + "="*60)
    print("📋 部署总结")
    print("="*60)
    
    if not failed_steps:
        print("🎉 所有步骤都成功完成!")
        print("✅ DeepSeek 中文金融分析系统已准备就绪!")
        print()
        print("🚀 现在可以开始使用:")
        print("   python run_deepseek_chinese.py AAPL")
    else:
        print("⚠️ 以下步骤需要手动处理:")
        for step in failed_steps:
            print(f"   - {step}")
        print()
        print("💡 建议:")
        print("1. 检查网络连接")
        print("2. 确认 DeepSeek API 密钥正确")
        print("3. 查看详细错误信息")
        print("4. 参考 '完整部署文档.md'")
    
    print("\n📞 如需帮助，请查看:")
    print("   - 完整部署文档.md")
    print("   - DeepSeek直接API使用指南.md")
    print("   - ChromaDB_Fix_README.md")

if __name__ == "__main__":
    main()
