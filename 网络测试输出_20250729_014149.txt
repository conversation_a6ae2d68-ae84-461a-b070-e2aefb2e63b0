网络连接和控制台输出测试 - 完整输出
============================================================
运行时间: 2025-07-29 01:41:49
============================================================

=== 控制台输出内容 ===

🔧 网络连接和控制台输出测试工具
============================================================
运行时间: 2025-07-29 01:40:50
🔍 开始网络连接测试...
==================================================

📡 测试直连:
   测试 百度 (https://www.baidu.com)...
   ✅ 百度 连接成功 (状态码: 200)
   测试 Google (https://www.google.com)...
   ❌ Google 连接失败: HTTPSConnectionPool(host='www.google.com', port=443): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x0000027E46DF6B10>, 'Connection to www.google.com timed out. (connect timeout=10)'))
   测试 DeepSeek API (https://api.deepseek.com)...
   ✅ DeepSeek API 连接成功 (状态码: 404)
   测试 OpenAI API (https://api.openai.com)...
   ❌ OpenAI API 连接失败: HTTPSConnectionPool(host='api.openai.com', port=443): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x0000027E46DF66C0>, 'Connection to api.openai.com timed out. (connect timeout=10)'))

🔄 测试代理连接:
   测试 HTTP代理 7890...
   ❌ HTTP代理 7890 连接失败: HTTPSConnectionPool(host='api.deepseek.com', port=443): Max retries exceeded with url: / (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000027E46DF7530>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
   测试 HTTP代理 10809...
   ❌ HTTP代理 10809 连接失败: HTTPSConnectionPool(host='api.deepseek.com', port=443): Max retries exceeded with url: / (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000027E46DF7E30>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
   测试 SOCKS5代理 10808...
   ❌ SOCKS5代理 10808 连接失败: SOCKSHTTPSConnectionPool(host='api.deepseek.com', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x0000027E46D44170>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))

🤖 测试 DeepSeek API 连接...
==================================================
   发送测试请求到 DeepSeek API...
   ✅ DeepSeek API 连接成功!
   📝 API 响应: 由于我无法实时获取天气信息，建议您通过以下方式查询：  
1. **手机天气应用**（如墨迹天气、彩云天气等）  
2. **网页搜索**（输入“城市名+天气”，如

📊 模拟分析过程...
==================================================
🚀 DeepSeek 中文金融分析系统
✅ 已设置 DeepSeek API 密钥: sk-3dc31...
✅ 已设置 FinnHub API 密钥

📊 分析设置:
   股票代码: TEST
   分析日期: 2025-07-29
   使用模型: deepseek-chat
   输出语言: 中文

🔄 正在初始化系统...
✅ 系统初始化成功!

🤖 开始模拟分析...
⏳ 正在调用 API...
   📈 获取市场数据...
   📰 分析新闻情感...
   💬 处理社交媒体数据...
   📊 进行技术分析...
   🎯 生成投资建议...

======================================================================
🎯 模拟分析报告
======================================================================
这是一个模拟的分析报告内容。
在实际运行中，这里会包含详细的金融分析结果。
包括技术指标、基本面分析、市场情绪等信息。
======================================================================

🎉 所有测试完成! DeepSeek API 连接正常
