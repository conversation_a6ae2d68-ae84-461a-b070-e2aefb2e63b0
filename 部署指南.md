# TradingAgents 部署指南

## 快速部署步骤

### 1. 环境准备
```bash
# 确保 Python 3.8+ 已安装
python --version

# 安装依赖
pip install -r requirements.txt
# 或者如果没有 requirements.txt
pip install numpy openai yfinance pandas requests
```

### 2. 修复 ChromaDB 问题
```bash
# 运行修复脚本
python fix_chromadb_issue.py
```

### 3. 配置 API 密钥

#### 方法1：直接在代码中设置（推荐）
编辑 `run_deepseek.py` 文件，找到第24行：
```python
YOUR_DEEPSEEK_API_KEY = "在这里粘贴您的DeepSeek API密钥"
```

#### 方法2：设置环境变量
```bash
# Windows
set OPENAI_API_KEY=your_deepseek_api_key

# Linux/Mac
export OPENAI_API_KEY=your_deepseek_api_key
```

### 4. 获取 DeepSeek API 密钥
1. 访问 https://platform.deepseek.com/
2. 注册账号并登录
3. 在控制台获取 API 密钥
4. 将密钥粘贴到配置中

### 5. 运行项目

#### 自动化运行（推荐）
```bash
python run_auto.py
```

#### 交互式运行
```bash
python run_deepseek.py
```

#### 指定股票和日期
```bash
python run_auto.py TSLA 2  # 分析特斯拉，2天前的数据
python run_auto.py AAPL 1  # 分析苹果，1天前的数据
```

## 常见问题解决

### 问题1：ChromaDB DLL 错误
```
ImportError: DLL load failed while importing chromadb_rust_bindings
```
**解决方案**：运行 `python fix_chromadb_issue.py`

### 问题2：网络连接问题
如果在中国大陆，可能需要设置代理：
```python
# 在 run_auto.py 中修改代理设置
proxy_url = "http://127.0.0.1:10809"  # 替换为你的代理地址
```

### 问题3：API 密钥错误
```
The api_key client option must be set
```
**解决方案**：确保正确设置了 DeepSeek API 密钥

### 问题4：Yahoo Finance 速率限制
```
YFRateLimitError: Too Many Requests
```
**解决方案**：等待几分钟后重试，或者使用不同的日期

## 项目结构
```
TradingAgents1/
├── run_auto.py              # 自动化运行脚本
├── run_deepseek.py          # 交互式运行脚本
├── fix_chromadb_issue.py    # ChromaDB 修复脚本
├── deepseek_config.py       # DeepSeek 配置
├── tradingagents/           # 主要代码
│   ├── agents/              # 分析师代理
│   ├── graph/               # 工作流图
│   └── dataflows/           # 数据流
└── ChromaDB_Fix_README.md   # 详细修复说明
```

## 使用示例

### 分析苹果股票
```bash
python run_auto.py AAPL
```

### 分析特斯拉股票（3天前数据）
```bash
python run_auto.py TSLA 3
```

### 查看分析结果
分析完成后，结果会保存在以下文件中：
```
deepseek_analysis_AAPL_20250128_143022.txt
```

## 配置选项

### DeepSeek 模型选择
在 `run_deepseek.py` 中可以选择：
1. 直接 API（推荐）- 使用 DeepSeek 官方 API
2. 标准版 - 通过 OpenRouter
3. V3版本 - 更强大的模型

### 分析师团队
默认使用所有分析师：
- 市场分析师（market）
- 社交媒体分析师（social）
- 新闻分析师（news）
- 基础面分析师（fundamentals）

## 性能优化

### 1. 减少 API 调用
- 使用缓存的数据
- 避免频繁请求相同股票

### 2. 网络优化
- 设置合适的代理
- 使用稳定的网络连接

### 3. 内存优化
- 修复后的版本使用内存存储，重启后数据会清除
- 这对于独立分析是正常的

## 故障排除清单

1. ✅ Python 版本 3.8+
2. ✅ 依赖包已安装
3. ✅ ChromaDB 问题已修复
4. ✅ DeepSeek API 密钥已设置
5. ✅ 网络连接正常
6. ✅ 代理设置正确（如需要）

## 技术支持

如果遇到问题：
1. 查看错误信息
2. 检查上述清单
3. 运行 `python fix_chromadb_issue.py` 重新修复
4. 提供详细的错误信息寻求帮助

## 更新日志

- **v1.1**：修复 ChromaDB DLL 加载问题
- **v1.0**：初始版本
